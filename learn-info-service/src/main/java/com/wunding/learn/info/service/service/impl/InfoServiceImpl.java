package com.wunding.learn.info.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.http.WebConstantUtil;
import com.wunding.learn.common.constant.info.InfoErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.ResourceChangeEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.dto.ResourceStatusDTO;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.CoursewareOnlineTempInfo;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ZuoKeFeign;
import com.wunding.learn.flowable.api.constant.AuditStatusEnum;
import com.wunding.learn.flowable.api.design.template.ApplyAuditTemplate;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.info.service.admin.dto.FileUpload;
import com.wunding.learn.info.service.admin.dto.InfoDTO;
import com.wunding.learn.info.service.admin.dto.InfoDetailDTO;
import com.wunding.learn.info.service.admin.dto.InfoPreviewDTO;
import com.wunding.learn.info.service.admin.dto.InfoPublishStatusDTO;
import com.wunding.learn.info.service.admin.dto.InfoTopStatusDTO;
import com.wunding.learn.info.service.admin.dto.SaveOrUpdateInfoDTO;
import com.wunding.learn.info.service.admin.query.InfoQuery;
import com.wunding.learn.info.service.client.dto.InfoClientDTO;
import com.wunding.learn.info.service.client.dto.InfoDetailClientDTO;
import com.wunding.learn.info.service.client.query.InfoClientQuery;
import com.wunding.learn.info.service.client.query.InfoDetailQuery;
import com.wunding.learn.info.service.client.query.InfoHomePageQuery;
import com.wunding.learn.info.service.client.query.InfoSearchQuery;
import com.wunding.learn.info.service.component.InfoViewLimitComponent;
import com.wunding.learn.info.service.dao.InfoDao;
import com.wunding.learn.info.service.enums.InfoContentType;
import com.wunding.learn.info.service.event.InfoViewEvent;
import com.wunding.learn.info.service.mapper.InfoMapper;
import com.wunding.learn.info.service.model.Info;
import com.wunding.learn.info.service.model.InfoCategory;
import com.wunding.learn.info.service.model.InfoView;
import com.wunding.learn.info.service.service.IInfoCategoryService;
import com.wunding.learn.info.service.service.IInfoService;
import com.wunding.learn.info.service.service.IInfoViewService;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 资讯表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2022-08-02
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("infoInfoService")
public class InfoServiceImpl extends ServiceImpl<InfoMapper, Info> implements IInfoService {

    /**
     * 文件上传
     */
    private static final Integer FILE_UPLOAD = 1;
    private final UserFeign userFeign;
    private final OrgFeign orgFeign;
    private final FileFeign fileFeign;
    private final ZuoKeFeign zuoKeFeign;
    private final IInfoCategoryService infoCategoryService;
    private final IInfoViewService infoViewService;
    private final InfoViewLimitComponent infoViewLimitComponent;
    private final MqProducer mqProducer;
    private final CommentFeign commentFeign;
    private final ExportComponent exportComponent;
    private final PushFeign pushFeign;
    private final ProcessFeign processFeign;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ApplyAuditTemplate<Info> infoApplyAuditTemplate;
    @Resource(name = "infoDao")
    private InfoDao infoDao;

    @Resource
    private ICategorysService categorysService;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    @Lazy
    private IResourceViewLimitService resourceViewLimitService;

    @Override
    public boolean topOrCancelInfo(InfoTopStatusDTO infoTopStatusDTO) {

        List<Info> infos = listByIds(infoTopStatusDTO.getInfoIdList());
        for (Info info : infos) {
            info.setIsTop(infoTopStatusDTO.getTopStatus());
            if (!infoDao.updateInfo(info)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void publishOrCancelInfo(InfoPublishStatusDTO infoPublishStatusDTO) {
        // 是否有课程存在审核流程或者审核不通过
        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), infoPublishStatusDTO.getPublishStatus())
            && baseMapper.isExistInfoNeedAudit(
            infoPublishStatusDTO.getInfoIdList())) {
            throw new BusinessException(InfoErrorNoEnum.EXIST_INFO_NEED_AUDIT);
        }
        List<String> infoIdList = infoPublishStatusDTO.getInfoIdList();
        List<Info> infoList = listByIds(infoIdList);
        // 过滤出是附件上传且未完成的资讯
        List<Info> existInfoList = infoList.stream().filter(info -> FILE_UPLOAD.equals(info.getInfomationSource()))
            .filter(info -> info.getTransformStatus() != TranscodeStatusEnum.TRANSFORMED.value)
            .collect(Collectors.toList());
        if (!existInfoList.isEmpty()) {
            List<String> existsLoginName = infoList.stream().map(Info::getTitle).collect(Collectors.toList());
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("资讯 ");
            for (String loginName : existsLoginName) {
                stringBuilder.append(loginName).append(",");
            }
            // 删除最后一个逗号
            stringBuilder.deleteCharAt(stringBuilder.length() - 1).append(" 资讯转码非完成状态");
            throw new BusinessException(InfoErrorNoEnum.INFO_TRANSFORMED_NOT_COMPLETED);
        }
        Date setDate = new Date();
        String userId = UserThreadContext.getUserId();
        if (infoPublishStatusDTO.getPublishStatus().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            setDate = null;
            userId = StringUtils.EMPTY;
        }

        // 更新资源评论状态
        List<ResourceStatusDTO> list = new ArrayList<>();
        ResourceStatusDTO dto;
        for (Info info : infoList) {
            dto = new ResourceStatusDTO();
            BeanUtils.copyProperties(info, dto);
            dto.setResourceId(info.getId());
            dto.setResourceType(ResourceChangeEnum.info.getValue());
            dto.setIsPublish(infoPublishStatusDTO.getPublishStatus());
            list.add(dto);
        }
        // 发送资源修改信息
        ResourceChangeEvent event = new ResourceChangeEvent(FirstInfoContentEnum.info.name(),
            infoPublishStatusDTO.getInfoIdList(), GeneralJudgeEnum.NEGATIVE.getValue(),
            infoPublishStatusDTO.getPublishStatus());
        event.setResourceStatusList(list);
        mqProducer.sendMsg(event);

        // 发送资源操作事件消息
        if (!CollectionUtils.isEmpty(infoIdList)) {
            infoIdList.forEach(id -> mqProducer.sendMsg(new ResourceOperateEvent(
                Objects.equals(infoPublishStatusDTO.getPublishStatus(), GeneralJudgeEnum.NEGATIVE.getValue())
                    ? OperationEnum.PUBLISH_CANCEL
                    : OperationEnum.PUBLISH, PushType.NEWS.getKey(), id)));
        }

        // 遍历执行发布/取消发布操作，同时记录业务日志
        for (Info info : infoList) {
            info.setPublishTime(setDate);
            info.setPublishBy(userId);
            info.setIsPublish(infoPublishStatusDTO.getPublishStatus());

            if (Objects.equals(infoPublishStatusDTO.getPublishStatus(), GeneralJudgeEnum.NEGATIVE.getValue())) {
                infoDao.unPublish(info);
            } else {
                infoDao.publish(info);
            }
        }
    }

    @Override
    public InfoPreviewDTO preview(String id) {
        InfoPreviewDTO preview = baseMapper.preview(id);
        String author = preview.getAuthor();
        UserDTO userById = userFeign.getUserById(author);
        preview.setFullName(Optional.ofNullable(userById).map(UserDTO::getFullName).orElse(""));
        preview.setHtmlUrl(fileFeign.getFileUrl(id, FileBizType.InfoWareFile.name()));
        return preview;
    }

    @Override
    public PageInfo<InfoDTO> findInfoByPage(InfoQuery infoQuery) {
        log.info("find_info_by_page_arg:{}", infoQuery);
        // 查询条件是否非空
        boolean condition = !StringUtils.isBlank(infoQuery.getAuthor());
        if (condition) {
            String author = infoQuery.getAuthor();
            if (StringUtils.isNotEmpty(author)) {
                String[] userIdArray = author.split(",");
                infoQuery.setAuthorList(Arrays.asList(userIdArray));
            }
        }
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        infoQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        infoQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        infoQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<InfoDTO> pageInfo = PageMethod.startPage(infoQuery.getPageNo(), infoQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectInfoByPage(infoQuery));
        List<InfoDTO> list = pageInfo.getList();
        if (list.isEmpty()) {
            return pageInfo;
        }
        Set<String> userIdList = list.stream().map(InfoDTO::getPublishBy)
            .filter(publishBy -> !StringUtil.isEmpty(publishBy)).collect(Collectors.toSet());
        Set<String> authorIdListAuthor = list.stream().map(InfoDTO::getAuthor)
            .filter(author -> !StringUtil.isEmpty(author)).collect(Collectors.toSet());
        userIdList.addAll(authorIdListAuthor);
        Map<String, UserDTO> userMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(userIdList)) {
            userMap = userFeign.getUserNameMapByIds(userIdList);
        }
        Map<String, UserDTO> finalUserMap = userMap;
        list.forEach(info -> {
            if (StringUtils.isNotEmpty(info.getPublishBy())) {
                UserDTO userDTO = finalUserMap.get(info.getPublishBy());
                info.setPublishByFullName(userDTO.getFullName());
            }
            if (info.getPublishTime() == null || info.getIsPublish() == 0) {
                info.setPublishTime(null);
            }
            info.setAuthorFullName(
                Optional.ofNullable(finalUserMap.get(info.getAuthor())).map(UserDTO::getFullName).orElse(null));
            info.setHtmlUrl(fileFeign.getFileUrl(info.getId(), FileBizType.InfoWareFile.name()));
            // 替换枚举值成对应字符串
            AuditStatusEnum.transCodeAndSetToStr(info);
        });
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateInfo(SaveOrUpdateInfoDTO saveOrUpdateInfoDTO) {
        log.info("save_or_update_info_arg:{}", saveOrUpdateInfoDTO);
        boolean result;
        if (StringUtils.isNotEmpty(saveOrUpdateInfoDTO.getInfoId())) {
            log.debug("into_update_info");
            result = updateInfo(saveOrUpdateInfoDTO);
        } else {
            // 当下发范围为仅创建人可见,不用鉴权
            if (saveOrUpdateInfoDTO.getViewType() != 1 && saveOrUpdateInfoDTO.getIsTrain() == 0) {
                //对下发范围进行鉴权
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(saveOrUpdateInfoDTO.getProgrammeId());
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
            log.debug("into_save_info");
            result = saveInfo(saveOrUpdateInfoDTO);
            mqProducer.sendMsg(new ExcitationInitMqEvent(
                new ResourceConfigInitDTO().setResourceId(saveOrUpdateInfoDTO.getInfoId())
                    .setResourceType(ExcitationEventCategoryEnum.NEWS.getCode())));
        }
        if (result) {
            Info info = getById(saveOrUpdateInfoDTO.getInfoId());
            if (info != null) {
                // 更新资源评论状态
                List<ResourceStatusDTO> list = new ArrayList<>();
                ResourceStatusDTO dto = new ResourceStatusDTO();
                BeanUtils.copyProperties(info, dto);
                dto.setResourceId(info.getId());
                dto.setResourceType(ResourceChangeEnum.info.getValue());
                list.add(dto);
                // 发送资源修改信息
                ResourceChangeEvent event = new ResourceChangeEvent(null, null, null, null);
                event.setResourceStatusList(list);
                mqProducer.sendMsg(event);
            }
        }
        handleCategoryCanDel();
        return result;
    }

    private void handleCategoryCanDel() {
        List<Info> exampleList = list();
        if (CollectionUtils.isEmpty(exampleList)) {
            categorysService.deleteCategoryByType(CategoryType.InfoCate.name());
            return;
        }
        List<String> collect = exampleList.stream().map(Info::getInfoCateId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            categorysService.deleteCategoryByType(CategoryType.InfoCate.name());
            return;
        }
        List<InfoCategory> infoCategoryList = infoCategoryService.listByIds(collect);
        List<String> categoryIdList = infoCategoryList.stream().map(InfoCategory::getCategoryId)
            .collect(Collectors.toList());
        categorysService.updateByIsCanDelCategoryId(categoryIdList, CategoryType.InfoCate.name());
    }


    @Override
    public PageInfo<InfoClientDTO> getInfoClientByPage(InfoClientQuery infoClientQuery) {
        infoClientQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<InfoClientDTO> pageInfo = PageMethod.startPage(infoClientQuery.getPageNo(),
                infoClientQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectInfoClientPage(infoClientQuery));
        pageInfo.setIsLastPage(infoClientQuery.getPageSize() != pageInfo.getList().size());
        return handleInfoResult(pageInfo);
    }

    /**
     * 处理资讯结果
     *
     * @param pageData
     */
    private PageInfo<InfoClientDTO> handleInfoResult(PageInfo<InfoClientDTO> pageData) {
        List<InfoClientDTO> result = pageData.getList();
        if (result.isEmpty()) {
            return pageData;
        }
        Set<String> infoIds = result.stream().map(InfoClientDTO::getId).collect(Collectors.toSet());
        Map<String, NamePath> infoImageMap = fileFeign.getImageFileNamePathsByBizIds(infoIds,
                ImageBizType.InfoImgIcon.toString()).stream()
            .collect(Collectors.toMap(NamePath::getCategoryId, namePath -> namePath, (key1, key2) -> key1));
        result.forEach(info -> {
            NamePath namePath = infoImageMap.get(info.getId());
            info.setInfoImage(namePath).setIsNewest(calculateNewest(info.getPublishTime()));
        });

        return pageData;
    }

    /**
     * 计算是否是最新发帖
     *
     * @param publishTime 出版时间
     * @return {@link Integer}
     */
    private Integer calculateNewest(Date publishTime) {
        String nDate = Optional.ofNullable(paraFeign.getParaValue("news_1"))
            .orElse(null);
        if (!StringUtils.isEmpty(nDate)) {
            Date date = new Date();
            // 这样得到的差值是微秒级别
            long diff = date.getTime() - publishTime.getTime();
            long days = diff / (1000 * 60 * 60 * 24);
            if (days >= Long.parseLong(nDate)) {
                return 0;
            } else {
                return 1;
            }
        } else {
            return 0;
        }
    }

    @Override
    public InfoDetailDTO detail(String id) {
        InfoDetailDTO infoDetailDTO = baseMapper.getInfoDetail(id);
        // 返回归属部门名称
        infoDetailDTO.setOrgName(orgFeign.getById(infoDetailDTO.getOrgId()).getOrgName());
        String author = infoDetailDTO.getAuthor();
        UserDTO userById = userFeign.getUserById(author);
        infoDetailDTO.setFullName(Optional.ofNullable(userById).map(UserDTO::getFullName).orElse(""));
        infoDetailDTO.setInfoImage(fileFeign.getImageFileNamePath(id, ImageBizType.InfoImgIcon.toString()));
        // 获取下发范围
        infoDetailDTO.setLimit(infoViewLimitComponent.getViewLimitBaseInfo(id));
        return infoDetailDTO;
    }

    @Override
    public InfoDetailClientDTO getInfoClientDetail(InfoDetailQuery query) {
        String infoId = query.getInfoId();
        String userId = UserThreadContext.getUserId();
        // 校验资讯是否存在
        Info info = checkInfo(infoId);
        // 校验下发范围
        checkPermission(infoId);
        // 响应数据对象处理
        InfoDetailClientDTO infoDetailClientDTO = new InfoDetailClientDTO();
        BeanUtils.copyProperties(info, infoDetailClientDTO);
        // 获取转码后的 index.html
        infoDetailClientDTO.setHtmlUrl(fileFeign.getFileUrl(infoId, FileBizType.InfoWareFile.name()));

        // 校验是否浏览过
        boolean exists = infoViewService.lambdaQuery().eq(InfoView::getInfoId, infoId).eq(InfoView::getCreateBy, userId)
            .exists();
        if (!exists) {
            InfoView infoView = new InfoView();
            infoView.setId(newId());
            infoView.setInfoTypeId(info.getInfoCateId());
            infoView.setInfoId(infoId);
            infoView.setOrgId(UserThreadContext.getOrgId());
            infoViewService.save(infoView);
            // 刷新浏览数
            mqProducer.sendMsg(new InfoViewEvent(infoId));
        }
        // 添加点击查看资讯事件积分埋点
        mqProducer.sendMsg(new ExcitationMQEvent(new ExcitationMQEventDTO().setUserId(userId)
            .setEventId(ExcitationEventEnum.viewNews.name())
            .setTargetId(infoDetailClientDTO.getId())
            .setTargetName(infoDetailClientDTO.getTitle())
            .setBizType(query.getBizType())
            .setBizId(query.getBizId())
            .setIsExchange(query.getIsExchange())
        ));
        return infoDetailClientDTO;
    }

    private boolean saveInfo(SaveOrUpdateInfoDTO saveInfo) {
        // 资讯主键id
        String infoId = newId();
        // 获取当前登录用户的id
        Info info = new Info();
        BeanUtils.copyProperties(saveInfo, info);
        // 保存资讯信息
        info.setId(infoId);
        info.setInfoIntro(StringUtil.getBrief(info.getInfoContent()));
        // 暂时默认为图文资讯
        info.setContentType(InfoContentType.IMAGE_NEWS.name());
        info.setOrgId(UserThreadContext.getOrgId());

        if (FILE_UPLOAD.equals(saveInfo.getInfomationSource())) {
            // 文件解码生产者
            FileUpload uploadFile = saveInfo.getUploadFile();
            if (StringUtils.isEmpty(uploadFile.getPath())) {
                throw new BusinessException(FileErrorNoEnum.ERR_FILE_NOT_NULL);
            }
            ApiAssert.notNullParams(uploadFile.getPath());
            SaveFileDTO saveFileDTO = fileFeign.saveFile(infoId, FileBizType.InfoWareFile.name(), uploadFile.getName(),
                uploadFile.getPath());
            // 发送消息转码
            mqProducer.sendMsg(
                new TransCodeEvent(saveFileDTO.getId(), infoId, FileBizType.InfoWareFile.name(), saveFileDTO.getPath(),
                    uploadFile.getMime(), uploadFile.getType(), null));
            // 设置资讯转码状态,文件名,文件类型
            info.setFileType(uploadFile.getMime());
            info.setTransformStatus(TranscodeStatusEnum.TRANSFORMING.value);
            info.setFileName(uploadFile.getName());
        } else {
            // 校验正文内容
            if (StringUtils.isEmpty(saveInfo.getInfoContent())) {
                throw new BusinessException(InfoErrorNoEnum.CONTENT_IS_BLANK);
            }
            // 生成index.html文件
            handleOnline(saveInfo.setInfoId(infoId));
            // 设置文件类型
            info.setFileType(WebConstantUtil.CW_CONTENT_TYPE_TEXT_HTML);
        }

        // 资讯图片处理
        NamePath infoImage = saveInfo.getInfoImage();
        if (infoImage != null && StringUtils.isNotEmpty(infoImage.getPath())) {
            fileFeign.saveImage(infoId, ImageBizType.InfoImgIcon.toString(), infoImage.getName(), infoImage.getPath());
        }

        // 保存资讯分类
        InfoCategory category = new InfoCategory();
        String cateId = newId();
        category.setId(cateId);
        category.setInfoId(infoId);
        category.setCategoryId(info.getInfoCateId());
        category.setCategoryType(CategoryType.InfoCate.toString());
        infoCategoryService.save(category);

        // 在生成分类之前初始化审核
        info.setCategoryId(category.getCategoryId());
        infoApplyAuditTemplate.insertResourceProcessSceneHandle(info);

        // 是否有资讯存在审核流程或者审核不通过
        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), info.getIsPublish())
            && !(info.getAuditStatus() == AuditStatusEnum.APPROVED.getCode()
            || info.getAuditStatus() == AuditStatusEnum.NOT_APPLICABLE.getCode())) {
            throw new BusinessException(InfoErrorNoEnum.EXIST_INFO_NEED_AUDIT);
        }

        // 存在下发范围
        if (saveInfo.getViewType() != null) {
            log.info("exec_info_view_limit:{},info_id:{}", saveInfo.getProgrammeId(), infoId);
            infoViewLimitComponent.handleNewViewLimit(saveInfo.getProgrammeId(), infoId);
        }
        info.setInfoCateId(cateId);
        saveInfo.setInfoId(info.getId());

        // 默认发布时
        Integer isPublish = saveInfo.getIsPublish();
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            info.setIsPublish(isPublish);
            info.setPublishBy(UserThreadContext.getUserId());
            info.setPublishTime(new Date());
        }

        // 启用才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveInfo.getIsPublish())
            && Optional.ofNullable(saveInfo.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(saveInfo, 0);
        }

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveInfo.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.NEWS.getKey(), info.getId()));

        return infoDao.saveInfo(info);
    }

    private boolean updateInfo(SaveOrUpdateInfoDTO updateInfo) {
        Info info = new Info();
        BeanUtils.copyProperties(updateInfo, info);
        String infoId = updateInfo.getInfoId();
        info.setId(infoId);
        // 更新操作只允许在线做课
        if (!FILE_UPLOAD.equals(updateInfo.getInfomationSource())) {
            // 校验正文内容
            if (StringUtils.isEmpty(updateInfo.getInfoContent())) {
                throw new BusinessException(InfoErrorNoEnum.CONTENT_IS_BLANK);
            }
            //删除之前的旧文件
            fileFeign.deleteFileByBizIdAndBizType(infoId, FileBizType.InfoWareFile.name(), 0);
            // 生成index.html文件
            handleOnline(updateInfo);
            // 设置file类型
            info.setFileType(WebConstantUtil.CW_CONTENT_TYPE_TEXT_HTML);
        }

        NamePath infoImage = updateInfo.getInfoImage();

        NamePath imageFileNamePath = fileFeign.getImageFileNamePath(infoId, ImageBizType.InfoImgIcon.toString());
        // 存在图片并且图片路径不同
        if (infoImage != null && StringUtils.isNotEmpty(infoImage.getPath())) {
            fileFeign.deleteImageByBizIdAndBizType(infoId, ImageBizType.InfoImgIcon.toString());
            fileFeign.saveImage(infoId, ImageBizType.InfoImgIcon.toString(), infoImage.getName(), infoImage.getPath());
        } else {
            // 如果未发送图片则删除
            Optional.ofNullable(imageFileNamePath).map(NamePath::getId).ifPresent(fileFeign::deleteImageByImageId);
        }

        // 分类处理,有变化则更新
        Optional<InfoCategory> infoCategory = infoCategoryService.lambdaQuery().eq(InfoCategory::getInfoId, infoId)
            .oneOpt();
        Info oldInfo = getById(infoId);
        info.setAuditStatus(oldInfo.getAuditStatus());
        infoCategory.ifPresentOrElse(item -> {
            if (!StringUtils.equals(item.getCategoryId(), updateInfo.getInfoCateId())) {
                infoCategoryService.lambdaUpdate().set(InfoCategory::getCategoryId, updateInfo.getInfoCateId())
                    .eq(InfoCategory::getInfoId, infoId).update();
            }
            // 在更新分类之前判断分类是否变更
            oldInfo.setCategoryId(infoCategory.map(InfoCategory::getCategoryId).orElse(""));
            info.setCategoryId(info.getInfoCateId());
            infoApplyAuditTemplate.updateResourceProcessSceneHandle(info, oldInfo);
        }, () -> {
            // 如果不存在分类则应该初始化数据
            infoApplyAuditTemplate.insertResourceProcessSceneHandle(info);
        });
        // 是否有资讯存在审核流程或者审核不通过

        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), info.getIsPublish())
            && !(info.getAuditStatus() == AuditStatusEnum.APPROVED.getCode()
            || info.getAuditStatus() == AuditStatusEnum.NOT_APPLICABLE.getCode())) {
            throw new BusinessException(InfoErrorNoEnum.EXIST_INFO_NEED_AUDIT);
        }
        makeInfoPublish(updateInfo, info);

        // 存在下发范围更新
        log.info("exec_info_view_limit:{},info_id:{}", updateInfo.getProgrammeId(), infoId);
        infoViewLimitComponent.handleNewViewLimit(updateInfo.getProgrammeId(), infoId);
        //更新头条下发范围
        mqProducer.sendMsg(new FirstInfoViewLimitChangeEvent(infoId, FirstInfoContentEnum.info.name(),
            updateInfo.getProgrammeId()));
        // 设置为null,表示不更新 info 表中的这个字段，只需要更新InfoCategory 表 就能更新类别的映射关系
        info.setInfoCateId(null);
        info.setInfoIntro(StringUtil.getBrief(info.getInfoContent()));

        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(updateInfo.getIsPublish())
            && Optional.ofNullable(updateInfo.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(updateInfo, 1);
        }

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(updateInfo.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.NEWS.getKey(), info.getId()));
        return infoDao.updateInfo(info);
    }

    private void makeInfoPublish(SaveOrUpdateInfoDTO updateInfo, Info info) {
        // 是否发布
        Integer isPublish = updateInfo.getIsPublish();
        if (isPublish != null) {
            //附件转码不成功前,不允许发布
            Info nowInfo = getById(info.getId());
            if (FILE_UPLOAD.equals(nowInfo.getInfomationSource())
                && nowInfo.getTransformStatus() != TranscodeStatusEnum.TRANSFORMED.value) {
                throw new BusinessException(InfoErrorNoEnum.INFO_TRANSFORMED_NOT_COMPLETED);
            }
            if (isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
                info.setIsPublish(isPublish);
                info.setPublishBy(UserThreadContext.getUserId());
                info.setPublishTime(new Date());
            } else if (isPublish == PublishStatusEnum.IS_NO_PUBLISH.getValue()) {
                info.setIsPublish(isPublish);
                info.setPublishBy(StringUtils.EMPTY);
                info.setPublishTime(null);
            }
        }
    }

    private void handleOnline(SaveOrUpdateInfoDTO saveOrUpdateInfoDTO) {
        // 构建html中divAuthor标签里的内容
        StringBuilder divAuthor = new StringBuilder();
        if (StringUtils.isNotEmpty(saveOrUpdateInfoDTO.getSource())) {
            divAuthor.append("来源: ").append(saveOrUpdateInfoDTO.getSource()).append(" ");
        }
        divAuthor.append(DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD_HHMM));
        // 在线做课课件临时文件信息
        CoursewareOnlineTempInfo coursewareOnlineTempInfo = zuoKeFeign.generateInfoZip(saveOrUpdateInfoDTO.getTitle(),
            saveOrUpdateInfoDTO.getInfoContent(), divAuthor.toString());

        //将zip包临时目录文件转移至正式目录
        fileFeign.saveSourceFile(saveOrUpdateInfoDTO.getInfoId(), FileBizType.InfoWareFile.name(),
            saveOrUpdateInfoDTO.getTitle() + ".zip", coursewareOnlineTempInfo.getZipTempPath());
        //将index.html临时目录文件转移至正式目录
        fileFeign.saveFile(saveOrUpdateInfoDTO.getInfoId(), FileBizType.InfoWareFile.name(),
            saveOrUpdateInfoDTO.getTitle(), coursewareOnlineTempInfo.getHtmlTempPath());
    }

    @Override
    public void infoExportData(InfoQuery query) {
        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List getData(Integer pageNo, Integer pageSize) {
                IInfoService service = SpringUtil.getBean("infoInfoService", IInfoService.class);
                query.setExport(true);
                query.setPageNo(pageNo);
                query.setPageSize(pageSize);
                // 不限制分页页码
                query.setExport(true);
                PageInfo<InfoDTO> pageInfo = service.findInfoByPage(query);
                List<Map<String, Object>> listExportDTOS = new ArrayList<>();

                for (InfoDTO dto : pageInfo.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(dto);
                    listExportDTOS.add(beanMap);
                }
                return listExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Info;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Info.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<InfoClientDTO> searchInfo(InfoSearchQuery infoSearchQuery) {
        InfoClientQuery infoClientQuery = new InfoClientQuery();
        BeanUtils.copyProperties(infoSearchQuery, infoClientQuery);
        infoClientQuery.setIsCount(true);
        return getInfoClientByPage(infoClientQuery);
    }

    @Override
    public List<String> getInvalidInfoId(Collection<String> infoIdList) {
        return baseMapper.getInvalidInfoId(infoIdList);
    }

    @Override
    public PageInfo<InfoClientDTO> findInfoHomePageList(InfoHomePageQuery infoHomePageQuery) {
        infoHomePageQuery.setUserId(UserThreadContext.getUserId());
        PageInfo<InfoClientDTO> pageData = PageMethod.startPage(infoHomePageQuery.getPageNo(),
                infoHomePageQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.selectInfoPageHomeList(infoHomePageQuery));
        pageData.setIsLastPage(infoHomePageQuery.getPageSize() > pageData.getList().size());
        return handleInfoResult(pageData);
    }

    @Override
    public void updateInfoInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.INFO_VIEW_EVENT:
                baseMapper.updateViewNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.INFO_COMMENT_EVENT:
                Map<String, Integer> commentNumMap = commentFeign.getDiscussCount(Collections.singleton(id),
                    CommentTypeEnum.INFO);
                lambdaUpdate().set(Info::getCommentNumber, commentNumMap.get(id)).eq(Info::getId, id).update();
                break;
            default:
        }
    }

    @Override
    public Info checkInfo(String id) {
        Info info = baseMapper.selectById(id);
        if (null == info) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        if (info.getIsPublish() == PublishEnum.NOT_PUBLISH.getValue()) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }
        return info;
    }

    @Override
    public void deleteInfoByIds(String ids) {
        String[] infoIds = ids.split(",");
        List<String> infoIdList = Arrays.asList(infoIds);
        if (infoIdList.isEmpty()) {
            return;
        }
        List<Info> delInfoList = this.listByIds(infoIdList);
        if (!CollectionUtils.isEmpty(
            delInfoList.stream().filter(i -> GeneralJudgeEnum.CONFIRM.getValue().equals(i.getIsPublish())).collect(
                Collectors.toSet()))) {
            throw new BusinessException(InfoErrorNoEnum.IS_PUBLISH_INFO);
        }
        delInfoList.forEach(e -> infoDao.delInfo(e));

        // 发送资源修改信息
        mqProducer.sendMsg(
            new ResourceChangeEvent(FirstInfoContentEnum.info.name(), infoIdList, GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));

        // 发送资源操作事件消息
        infoIdList.forEach(id -> mqProducer.sendMsg(
            new ResourceOperateEvent(OperationEnum.DELETE, PushType.NEWS.getKey(), id)));

        infoIdList.forEach(infoViewLimitComponent::delViewLimit);

        // 删除审核流程实例
        processFeign.deleteProcessInstanceAndTaskByResourceId(infoIdList);
        handleCategoryCanDel();
    }

    /**
     * 校验下发范围
     *
     * @param id 资讯id
     */
    private void checkPermission(String id) {
        String userId = UserThreadContext.getUserId();
        boolean b = infoViewLimitComponent.checkViewLimit(userId, id);
        if (!b) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    /**
     * 调用推送feign
     *
     * @param saveDTO      保存实时dto
     * @param operateState 操作状态
     */
    private void sendPushFeign(SaveOrUpdateInfoDTO saveDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(saveDTO.getInfoId(), ImageBizType.InfoImgIcon.name()));
                }
            });
        }

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveDTO.getIsTrain()).orElse(0);
        PushResourceDTO pushResourceDTO = new PushResourceDTO()
            .setIsTrain(isTrain)
            .setOperateState(operateState)
            .setProgrammeId(saveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveDTO.getInfoId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(saveDTO.getInfoId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setIntro(saveDTO.getDescriptions());

        @SuppressWarnings("unchecked")
        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

}
