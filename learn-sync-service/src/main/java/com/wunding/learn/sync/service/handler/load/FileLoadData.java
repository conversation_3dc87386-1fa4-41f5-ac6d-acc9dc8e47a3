package com.wunding.learn.sync.service.handler.load;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.sync.SyncErrorNoEnum;
import com.wunding.learn.common.constant.user.UserConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.net.FTPUtils;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.sync.service.admin.dto.FtpConfigDTO;
import com.wunding.learn.sync.service.handler.LoadData;
import com.wunding.learn.sync.service.handler.SaveData;
import com.wunding.learn.sync.service.handler.conver.GroovyConvertData;
import com.wunding.learn.sync.service.model.DataSource;
import com.wunding.learn.sync.service.model.SourceRecord;
import com.wunding.learn.sync.service.model.Task;
import com.wunding.learn.sync.service.model.lnns.SyncPostRecord;
import com.wunding.learn.sync.service.service.IDataSourceService;
import com.wunding.learn.sync.service.service.ISourceRecordService;
import com.wunding.learn.sync.service.service.ITaskService;
import com.wunding.learn.sync.service.service.lnns.IHrmOrgService;
import com.wunding.learn.sync.service.service.lnns.IHrmUserService;
import com.wunding.learn.sync.service.service.lnns.ISyncPostRecordService;
import com.wunding.learn.user.api.dto.NewIdentityFeignDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.sync.OrgSimpleInfo;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import com.wunding.learn.user.api.enums.IdentityCategoryEnum;
import com.wunding.learn.user.api.service.NewIdentityFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import jakarta.annotation.Nullable;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 文件加载数据
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-13
 */
@Service("fileLoadData")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class FileLoadData implements LoadData {

    private final IDataSourceService dataSourceService;
    private final ISourceRecordService sourceRecordService;
    private final SaveData saveData;
    private final ITaskService taskService;
    private final GroovyConvertData groovyConvertData;
    private final JdbcTemplate jdbcTemplate;
    private final IHrmOrgService hrmOrgService;
    private final IHrmUserService hrmUserService;
    private final OrgFeign orgFeign;
    private final FileFeign fileFeign;
    private final NewIdentityFeign newIdentityFeign;
    private final ISyncPostRecordService syncPostRecordService;
    @Value("${app.filePath}")
    private String zipFilePath;

    //    private static final String ORG_JOB_TABLE = "D_HRM_TB_ORG_JOB";
//    private static final String ORG_UNIT_TABLE = "D_HRM_TB_ORG_ORGUNIT";
//    private static final String ORG_UNITRELATION_NEW_TABLE = "D_HRM_TB_ORG_UNITRELATION_NEW";
//    private static final String STA_EMP_ORG_TABLE = "D_HRM_TB_STA_EMP_ORG";
//    private static final String STA_EMP_TABLE = "D_HRM_TB_STA_EMP";
    private static final String HRM_USER = "temp_expdata_hrm_renyuan";
    private static final String HRM_ORG = "temp_expdata_hrm_zhuzhi";


    // 定义前缀与处理方法的映射
    private final Map<String, BiConsumer<String, Boolean>> parsers = Maps.newLinkedHashMap();

    {
        parsers.put(HRM_USER, this::parseHrmUserData);
        parsers.put(HRM_ORG, this::parseHrmOrgData);
        // 必须存在顺序因为,按前缀去匹配,如果STA_EMP_TABLE不排中后面会导致匹配到D_HRM_TB_STA_EMP_ORG表
//        parsers.put(ORG_JOB_TABLE, this::parseOrgJobData);
//        parsers.put(ORG_UNIT_TABLE, this::parseOrgUnitData);
//        parsers.put(ORG_UNITRELATION_NEW_TABLE, this::parseOrgRelationData);
//        parsers.put(STA_EMP_ORG_TABLE, this::parseEmpOrgData);
//        parsers.put(STA_EMP_TABLE, this::parseEmployeeData);
    }

    private final Map<String, List<String>> headerMap = new HashMap<>();

    // 内存中的数据表
    private final Map<String, DataTable> dataTables = new HashMap<>();

    // 添加批处理大小配置 10w 行数据加载到内存
    private static final int BATCH_SIZE = 1_000_000;

    // 添加数据表类
    private static class DataTable {

        private final String tableName;
        private final List<Map<String, Object>> rows;
        private final Map<String, Integer> columnIndexes;
        private final JdbcTemplate jdbcTemplate;
        @Setter
        @Nullable
        private Boolean useDatabase = null;
        private final Map<String, Map<Object, List<Map<String, Object>>>> indexes = new HashMap<>();
        private boolean indexesBuilt = false;

        public DataTable(String tableName, List<String> headers, JdbcTemplate jdbcTemplate) {
            this.tableName = tableName.toLowerCase(Locale.ROOT);
            this.rows = new ArrayList<>();
            this.columnIndexes = Maps.newLinkedHashMapWithExpectedSize(headers.size());
            this.jdbcTemplate = jdbcTemplate;

            for (int i = 0; i < headers.size(); i++) {
                columnIndexes.put(headers.get(i), i);
            }
        }

        public void addRow(String[] values) {
            Map<String, Object> row = createRow(values);
            if (Boolean.TRUE.equals(useDatabase)) {
                // 如果数据还没满1w则添加到内存中
                if (rows.size() < 10_000) {
                    rows.add(row);
                } else {
                    rows.add(row);
                    //超过1w则插入到数据库中
                    batchInsertToDatabase();
                    rows.clear();
                }
            } else {
                rows.add(row);

                // 如果内存中的数据量超过阈值，切换到数据库存储
                if (rows.size() >= BATCH_SIZE) {
                    log.error("file Content Is Too Large:{}", rows.size());
                    switchToDatabase();
                }
            }
        }

        /**
         * 构建索引 在数据加载完成后调用此方法
         */
        public void buildIndexes() {
            if (indexesBuilt) {
                return;
            }

            if (Boolean.TRUE.equals(useDatabase)) {
                buildDatabaseIndexes();
            } else {
                buildMemoryIndexes();
            }
            indexesBuilt = true;
        }

        private void buildDatabaseIndexes() {
            // doNonthing
        }

        private void buildMemoryIndexes() {
            // 可以指定列构建索引
            for (Map<String, Object> row : rows) {
                for (String columnName : columnIndexes.keySet()) {
                    Object value = row.get(columnName);
                    if (value != null) {
                        indexes.computeIfAbsent(columnName, k -> new HashMap<>())
                            .computeIfAbsent(value, k -> new ArrayList<>())
                            .add(row);
                    }
                }
            }
        }

        private Map<String, Object> createRow(String[] values) {
            Map<String, Object> row = Maps.newHashMapWithExpectedSize(values.length);
            for (Entry<String, Integer> entry : columnIndexes.entrySet()) {
                String columnName = entry.getKey();
                int index = entry.getValue();
                if (index < values.length) {
                    row.put(columnName, values[index]);
                }
            }
            return row;
        }

        private void switchToDatabase() {
            if (useDatabase == null || !useDatabase) {
                log.info("未设置使用数据存储，继续使用内存存储: {}", tableName);
                return;
            }

            log.info("数据量超过阈值，切换到数据库存储: {}", tableName);
            useDatabase = true;

            // 将内存中的数据批量插入数据库
            if (!rows.isEmpty()) {
                batchInsertToDatabase();
                rows.clear();
            }
        }

        private void batchInsertToDatabase() {
            if (null == useDatabase || !useDatabase) {
                return;
            }
            if (rows.isEmpty()) {
                return;
            }

            List<String> columns = new ArrayList<>(columnIndexes.keySet());
            String insertSql = buildInsertSql(columns);

            List<Object[]> batchArgs = rows.stream()
                .map(row -> convertRowToArgs(row, columns))
                .collect(Collectors.toList());

            jdbcTemplate.batchUpdate(insertSql, batchArgs);
        }

        private String buildInsertSql(List<String> columns) {
            StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
            StringBuilder placeholders = new StringBuilder();

            for (int i = 0; i < columns.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                    placeholders.append(", ");
                }
                sql.append(columns.get(i));
                placeholders.append("?");
            }

            return sql.append(") VALUES (").append(placeholders).append(")").toString();
        }

        private Object[] convertRowToArgs(Map<String, Object> row, List<String> columns) {
            Object[] args = new Object[columns.size()];
            for (int i = 0; i < columns.size(); i++) {
                String columnName = columns.get(i);
                Object value = row.getOrDefault(columnName, null);

                if (value instanceof String stringInstance) {
                    if (isDateTimeColumn(columnName)) {
                        value = validateAndConvertDateTime(stringInstance);
                    }
                    if (StringUtils.isEmpty(stringInstance)) {
                        value = null;
                    }
                }

                args[i] = value;
            }
            return args;
        }

        /**
         * 检查列名是否可能是日期时间列
         */
        private boolean isDateTimeColumn(String columnName) {
            String lowerColumnName = columnName.toLowerCase();
            return lowerColumnName.contains("time") ||
                lowerColumnName.contains("date");
        }

        /**
         * 验证并转换日期时间值
         */
        private Object validateAndConvertDateTime(String value) {
            if (StringUtils.isBlank(value)) {
                return null;
            }

            try {
                // 尝试解析日期时间
                return DateUtil.formatToDate(value);
            } catch (Exception e) {
                log.warn("Invalid datetime value: {}, using null instead", value);
                return value;
            }
        }

        public List<Map<String, Object>> select(String[] columns, String whereClause) {
            if (!indexesBuilt) {
                buildIndexes();
            }

            if (Boolean.TRUE.equals(useDatabase)) {
                return selectFromDatabase(columns, whereClause);
            } else {
                return selectFromMemory(columns, whereClause);
            }
        }

        private List<Map<String, Object>> selectFromDatabase(String[] columns, String whereClause) {
            StringBuilder sql = new StringBuilder("SELECT ");
            sql.append(String.join(", ", columns));
            sql.append(" FROM ").append(tableName);
            if (whereClause != null && !whereClause.trim().isEmpty()) {
                sql.append(" WHERE ").append(whereClause);
            }

            return jdbcTemplate.query(sql.toString(), (rs, rowNum) -> {
                Map<String, Object> row = new HashMap<>();
                for (String column : columns) {
                    row.put(column, rs.getObject(column));
                }
                return row;
            });
        }

        private List<Map<String, Object>> selectFromMemory(String[] columns, String whereClause) {
            if (StringUtils.isBlank(whereClause)) {
                return rows.stream()
                    .map(row -> projectRow(row, columns))
                    .collect(Collectors.toList());
            }

            // 处理查询条件
            String[] conditions = whereClause.split("(?i)\\s+AND\\s+");
            List<List<Map<String, Object>>> indexedResults = new ArrayList<>();
            List<String> dynamicConditions = new ArrayList<>();

            for (String cond : conditions) {
                handleCondition(cond, indexedResults, dynamicConditions);
            }

            List<Map<String, Object>> candidateRows = getQualifiedRows(indexedResults);

            // 3. 对交集结果再用 evaluateWhereClause 精筛
            return candidateRows.stream()
                .filter(row -> {
                    if (CollectionUtils.isEmpty(dynamicConditions) && CollectionUtils.isEmpty(indexedResults)) {
                        return false;
                    }
                    for (String cond : dynamicConditions) {
                        if (!evaluateWhereClause(row, String.join(" ", cond))) {
                            return false;
                        }
                    }
                    return true;
                })
                .map(row -> projectRow(row, columns))
                .collect(Collectors.toList());
        }

        /**
         * 处理单个查询条件，分为索引匹配条件和动态条件
         */
        private void handleCondition(String condition, List<List<Map<String, Object>>> indexedResults,
            List<String> dynamicConditions) {
            String trimmedCond = condition.trim();
            String[] parts = trimmedCond.split("\\s+");

            if (parts.length == 3 && "=".equals(parts[1])) {
                String column = parts[0];
                String value = parts[2];
                Map<Object, List<Map<String, Object>>> columnIndex = indexes.get(column);

                if (columnIndex != null && columnIndex.containsKey(value)) {
                    indexedResults.add(columnIndex.get(value));
                } else {
                    // 索引未命中直接返回空结果
                    indexedResults.clear();
                }
            } else {
                dynamicConditions.add(trimmedCond);
            }
        }

        /**
         * 获取满足所有索引条件的行（交集）
         */
        private List<Map<String, Object>> getQualifiedRows(List<List<Map<String, Object>>> indexedResults) {
            if (indexedResults.isEmpty() || indexedResults.getFirst().isEmpty()) {
                return new ArrayList<>(rows);
            }

            List<Map<String, Object>> result = new ArrayList<>(indexedResults.getFirst());
            for (int i = 1; i < indexedResults.size(); i++) {
                result.retainAll(indexedResults.get(i));
            }
            return result;
        }

        /**
         * 只保留需要的列
         *
         * @param row     当前行
         * @param columns 需要查询的列
         * @return {@link Map }<{@link String }, {@link Object }>
         */
        private Map<String, Object> projectRow(Map<String, Object> row, String[] columns) {
            Map<String, Object> result = new HashMap<>();
            if (columns.length == 1 && columns[0].equals("*")) {
                result.putAll(row);
            } else {
                for (String column : columns) {
                    result.put(column, row.get(column));
                }
            }
            return result;
        }

        public void cleanup() {
            // 清理内存数据
            rows.clear();
            columnIndexes.clear();
            indexes.clear();
        }

        public void cleanTable() {
            // 1. 清理数据库当天数据
            if (Boolean.TRUE.equals(useDatabase)) {
                Set<String> whitelistTableName = Set.of("d_hrm_tb_sta_emp_org", "d_hrm_tb_org_job",
                    "d_hrm_tb_org_orgunit",
                    "d_hrm_tb_org_unitrelation_new", "d_hrm_tb_sta_emp", "hrm_user", "hrm_org");
                if (!whitelistTableName.contains(tableName)) {
                    return;
                }
                String sql = "delete from " + tableName + " where insert_date = ?";
                jdbcTemplate.update(sql, DateUtil.getYmdStr(new Date()));
            }
        }

        /**
         * 计算where子句
         *
         * @param row         当前行
         * @param whereClause WHERE子句
         * @return boolean 是否符合条件
         */
        private boolean evaluateWhereClause(Map<String, Object> row, String whereClause) {
            if (whereClause == null || whereClause.trim().isEmpty()) {
                return true;
            }
            // 简单的where条件解析，支持 =, >, <, >=, <=, !=
            String[] parts = whereClause.split("\\s+");
            if (parts.length != 3) {
                return true;
            }
            String column = parts[0];
            String operator = parts[1];
            String value = parts[2];

            Object columnValue = row.get(column);
            if (columnValue == null) {
                return false;
            }

            return switch (operator) {
                case "=" -> columnValue.toString().equals(value);
                // 下面的操作不一定有用
                case ">" -> getCompareTo(columnValue, value) > 0;
                case "<" -> getCompareTo(columnValue, value) < 0;
                case ">=" -> getCompareTo(columnValue, value) >= 0;
                case "<=" -> getCompareTo(columnValue, value) <= 0;
                case "!=" -> !columnValue.toString().equals(value);
                default -> true;
            };
        }

        private static int getCompareTo(Object columnValue, String value) {
            return new BigDecimal(columnValue.toString()).compareTo(new BigDecimal(value));
        }

    }

    public void getData(Long dataSourceId, @Nullable Boolean useDateBase) {
        //读取数据源数据和参数
        Optional<DataSource> dataSourceOpt = Optional.ofNullable(dataSourceService.getById(dataSourceId));
        // 可以是json也可以是文件的绝对路径
        String configContent = dataSourceOpt.map(DataSource::getPath).filter(StringUtils::isNotEmpty)
            .orElse(zipFilePath);
        FTPUtils ftpUtils = null;
        try {
            FtpConfigDTO ftpConfigDTO = JsonUtil.parseObject(configContent, FtpConfigDTO.class);
            if (Objects.isNull(ftpConfigDTO)) {
                throw new BusinessException(SyncErrorNoEnum.FTP_CONNECT_FAIL);
            }
            FTPClient ftpClient = new FTPClient();
            ftpUtils = new FTPUtils(ftpClient,
                ftpConfigDTO.getHost(),
                ftpConfigDTO.getPort(),
                ftpConfigDTO.getUsername(),
                ftpConfigDTO.getPassword());
            // 连接到FTP服务器
            ftpUtils.connect();
            String localFilePath = fileFeign.getCustomPhysicalPath("syncUser.zip", "sync");
            // 从FTP服务器获取数据文件
            boolean downloadFile = ftpUtils.downloadFile(ftpConfigDTO.getFilePath(), localFilePath);
            if (!downloadFile) {
                throw new BusinessException(SyncErrorNoEnum.DOWNLOAD_FILE_FAIL);
            }
            // 将文件路径替换成本地路径
            configContent = localFilePath;
        } catch (BusinessException businessException) {
            log.warn("业务异常", businessException);
            if (!businessException.getErrorCode().equals(SyncErrorNoEnum.FTP_CONNECT_FAIL.getErrorCode())) {
                throw businessException;
            }
        } catch (Exception e) {
            log.warn("解析FTP配置出错", e);
        } finally {
            Optional.ofNullable(ftpUtils).ifPresent(FTPUtils::disconnect);
        }
        if (StringUtils.isEmpty(configContent)) {
            throw new BusinessException(SyncErrorNoEnum.DATA_SOURCE_NOT_EXIST);
        }
        org.springframework.core.io.Resource resourceFile = new ClassPathResource(configContent);
        if (!resourceFile.exists()) {
            resourceFile = new FileSystemResource(configContent);
            if (!resourceFile.exists()) {
                throw new BusinessException(SyncErrorNoEnum.DATA_SOURCE_NOT_EXIST);
            }
        }
        List<EntryContent> txtEntries = new ArrayList<>();
        List<EntryContent> datEntries = new ArrayList<>();
        // 读取压缩包中的文件
        try (InputStream fis = new FileInputStream(resourceFile.getFile());
            BufferedInputStream bufferedIn = new BufferedInputStream(fis)) {
            String fileName = resourceFile.getFile().getName().toLowerCase();

            if (fileName.endsWith(".zip")) {
                // 处理 ZIP 文件
                processZipFile(bufferedIn, txtEntries, datEntries);
            } else if (fileName.endsWith(".tar.gz") || fileName.endsWith(".tgz")) {
                // 处理 tar.gz 格式
                processTarGzFile(bufferedIn, txtEntries, datEntries);
            } else if (fileName.endsWith(".gz")) {
                // 处理单个 .gz 文件
                processSingleGzFile(bufferedIn, fileName, txtEntries, datEntries);
            } else {
                // 处理未压缩文件
                processSingleFile(bufferedIn, fileName, txtEntries, datEntries);
            }
        } catch (IOException e) {
            log.error("读取压缩包中的文件出错", e);
            throw new BusinessException(SyncErrorNoEnum.LOAD_DATA_ERROR);
        }

        // 如果提供txt的表结构文件可以自动创建表先处理所有txt
//        for (EntryContent txt : txtEntries) {
//            List<String> headersFromDDL = parseHeadersFromDDL(txt.content);
//            String tableName = headersFromDDL.getFirst();
//            headersFromDDL.removeFirst();
//            headerMap.put(tableName, headersFromDDL);
//        }

// 固定俩个表头出来
        headerMap.put(HRM_USER, List.of("C_OPERATE_TIME",
            "C_EMPLOYEE_NAME", "C_EMPLOYEE_CODE",
            "C_COMPANY_HID",
            "C_COMPANY_NAME",
            "C_DEPT_HID",
            "C_DEPT_NAME",
            "C_POSITION_NAME",
            "C_JOB_NAME",
            "C_EMPLOYEE_STATUS",
            "C_ACTIVE_STATUS",
            "C_HEADCOUNT_TYPE",
            "C_MOBILE_TEL",
            "C_BUSINESS_EMAIL",
            "C_PER_EMAIL",
            "C_GENDER",
            "C_BIRTH_DATE",
            "C_OFFICE_TEL",
            "C_LABOR_DATE",
            "C_LABOR_TYPE",
            "C_PERSONAL_ID"
        ));
        headerMap.put(HRM_ORG, List.of(
            "C_OPERATE_TIME",
            "C_NAME",
            "C_CODE",
            "C_HID",
            "C_TYPE",
            "C_SUPERIOR_HID",
            "C_PATH_CODE",
            "C_STATUS"
        ));

        // 再处理所有dat
        datEntries.forEach(dat -> {
            for (Entry<String, BiConsumer<String, Boolean>> parser : parsers.entrySet()) {
                if (dat.name.startsWith(parser.getKey())) {
                    parser.getValue().accept(dat.content, useDateBase);
                    break;
                }
            }
        });
    }


    /**
     * <p> 辅助类,记录下文件内容
     *
     * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
     * @since 2025-05-14
     */
    private static class EntryContent {

        String name;
        String content;

        EntryContent(String name, String content) {
            this.name = name;
            this.content = content;
        }
    }

    @Override
    public void loadData(Long taskId, Long dataSourceId, Long mappingId) {
        // 执行之前先清理一遍map
        contextMap.clear();
        SourceRecord sourceRecord = sourceRecordService.initSave(taskId);
        try {
            getData(dataSourceId, true);
            // 按类型从数据库加载数据
            receiveOrgData(null, taskId, null);
            receivePostData(null, taskId, null);
            receiveJobLevelData(null, taskId, null);
            receiveUserData(null, taskId, null);
            sourceRecord.setStatus("success");
        } catch (Exception e) {
            log.error("file loadData 获取数据出错", e);
            sourceRecord.setStatus("error");
            sourceRecord.setErrorMsg(e.getMessage());
            sourceRecord.setContent("");
            throw new BusinessException(SyncErrorNoEnum.LOAD_DATA_ERROR);
        } finally {
            sourceRecordService.updateById(sourceRecord);
            // 1. 先清理每个 DataTable 的资源
            for (DataTable table : dataTables.values()) {
                table.cleanup();
            }

            // 2. 清空 map
            dataTables.clear();
            headerMap.clear();
            contextMap.clear();
        }

    }


    @Override
    public void receiveJobLevelData(List<Map<String, Object>> data, Long taskId, Long mappingId) {
        Task task = taskService.getById(taskId);
        if (task == null) {
            log.error("任务不存在");
            return;
        }
        List<String> list = convertJobLevelData();
        if (CollectionUtils.isEmpty(list)) {
            log.warn("职级数据为空");
            return;
        }
        // 保存同步职级数据
        saveData.saveJobLevel(taskId, list);
    }

    private List<String> convertJobLevelData() {
        return hrmUserService.queryJobLevelUserDTO();
    }

    /**
     * 接收组织数据
     *
     * @param data 接收的数据
     */
    public void receiveOrgData(@Nullable List<Map<String, Object>> data, Long taskId, @Nullable Long mappingId) {
        Task task = taskService.getById(taskId);
        if (task == null) {
            log.error("任务不存在");
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        OrgDTO org = orgFeign.getById(task.getOrgId());
        List<SyncOrgDTO> list = convertOrgData(org);
        if (CollectionUtils.isEmpty(list)) {
            log.error("组织数据为空");
            return;
        }
        // 设置部门LevelPath和levelName 先找出所有的root元素，即父级部门
        Queue<SyncOrgDTO> queues = list.stream().filter(l -> Objects.equals(l.getOrgCode(), org.getOrgCode()))
            .collect(Collectors.toCollection(LinkedList::new));

        list = groovyConvertData.findChildren(list, queues, org);

        // 保存同步组织数据
        saveData.saveSyncOrg(taskId, list);
    }

    /**
     * 接收岗位数据
     *
     * @param data 接收的数据
     */
    public void receivePostData(List<Map<String, Object>> data, Long taskId, Long mappingId) {
        Task task = taskService.getById(taskId);
        if (task == null) {
            log.error("post任务不存在");
            return;
        }
        List<SyncPostDTO> list = convertPostData();
        if (CollectionUtils.isEmpty(list)) {
            log.error("post数据为空");
            return;
        }
        // 用户那边需要使用这个上下文信息处理岗位
        contextMap.put("postCtx", list);
        // 保存同步岗位数据
        saveData.saveSyncPost(taskId, list);
    }
    /**
     * 接收用户数据
     *
     * @param data 接收的数据
     */
    @SuppressWarnings("unchecked")
    public void receiveUserData(List<Map<String, Object>> data, Long taskId, Long mappingId) {
        Task task = taskService.getById(taskId);
        if (task == null) {
            log.error("user任务不存在");
            return;
        }
        Optional<SyncOrgDTO> optSyncOrgDTO = Optional.ofNullable(hrmOrgService.queryRootSyncOrgDTO());
        List<SyncUserDTO> list = convertUserData();
        if (CollectionUtils.isEmpty(list)) {
            log.error("user数据为空");
            return;
        }
        List<SyncPostDTO> syncPostDTOList = (List<SyncPostDTO>) contextMap.get("postCtx");
        List<NewIdentityFeignDTO> jobLevelIdentityList = newIdentityFeign.getNewIdentityListByCategoryId(
            IdentityCategoryEnum.JOB_LEVEL.getCategoryId());
        Map<String, String> postMap = syncPostDTOList.stream()
            .collect(Collectors.toMap(SyncPostDTO::getName, SyncPostDTO::getCode, (k1, k2) -> k1));
        Map<String, NewIdentityFeignDTO> jobLevelMap = jobLevelIdentityList.stream()
            .collect(Collectors.toMap(NewIdentityFeignDTO::getName, Function.identity(), (k1, k2) -> k1));

        String rootOrgCode = optSyncOrgDTO.map(SyncOrgDTO::getOrgCode)
            .orElseGet(() -> orgFeign.getById(task.getOrgId()).getOrgCode());
        list.forEach(syncUserDTO -> {
            if (syncUserDTO.getOrgCode().equals(rootOrgCode) || StringUtils.isEmpty(syncUserDTO.getOrgCode())) {
                syncUserDTO.setOrgCode(rootOrgCode);
            }
            List<OrgSimpleInfo> orgCodeList = new ArrayList<>();
            OrgSimpleInfo orgSimpleInfo = new OrgSimpleInfo();
            orgSimpleInfo.setOrgId(syncUserDTO.getOrgCode());
            orgSimpleInfo.setAssociationType(1);
            orgCodeList.add(orgSimpleInfo);
            syncUserDTO.setOrgCodeList(orgCodeList);
            // 借用岗位code,存在岗位为空的情况，给默认岗位数据
            syncUserDTO.setPostCode(postMap.getOrDefault(syncUserDTO.getPostCode(), UserConstant.DEFAULT_POST_CODE));
            // 借用职级id，用于关联
            if (StringUtils.isNotBlank(syncUserDTO.getIdentityJobLevelId())) {
                Optional.ofNullable(jobLevelMap.get(syncUserDTO.getIdentityJobLevelId()))
                    .ifPresent(item -> syncUserDTO.setIdentityJobLevelId(item.getId()));
            }
        });
        // 保存同步用户数据
        saveData.saveSyncUser(taskId, list);
    }


    /**
     * 转换岗位数据
     *
     * @return {@link List }<{@link SyncPostDTO }>
     */
    private List<SyncPostDTO> convertPostData() {
        List<SyncPostDTO> list = hrmUserService.queryPostData();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
//        list.forEach(syncPostDTO -> {
//            syncPostDTO.setCategoryId(IdentityCategoryEnum.POST.getCategoryId());
//            // 默认岗位目录
//            syncPostDTO.setDirectoryId("0");
//        });
        // TODO 这里没有岗位编号,需要按岗位名称来生成 Id
        // 查询企业微信库存岗位数据Map
        Map<String, String> postMap = syncPostRecordService.list().stream()
            .collect(Collectors.toMap(SyncPostRecord::getName, SyncPostRecord::getId, (k1, k2) -> k1));

        // 保存更新企业微信岗位数据
        List<SyncPostRecord> saveList = new ArrayList<>();
        List<SyncPostRecord> updateList = new ArrayList<>();
        // 用一个备忘录进行去重
        Set<String> memoSet = Sets.newHashSet();
        list.forEach(postDTO -> {
            if (StringUtils.isEmpty(postDTO.getName()) && !memoSet.contains(postDTO.getName())) {
                return;
            }
            memoSet.add(postDTO.getName());
            // 保存数据用于后续的同步进行对比
            SyncPostRecord postWeCom = new SyncPostRecord().setId(
                    postMap.containsKey(postDTO.getName()) ? postMap.get(postDTO.getName())
                        : StringUtil.newId())
                .setName(postDTO.getName())
                .setCreateBy(UserThreadContext.getUserId())
                .setCreateTime(new Date())
                .setUpdateBy(UserThreadContext.getUserId())
                .setUpdateTime(new Date());

            // 实际传递的数据进行参数负值
            postDTO.setCategoryId(IdentityCategoryEnum.POST.getCategoryId());
            // 默认岗位目录
            postDTO.setDirectoryId("7");
            postDTO.setCode(postWeCom.getId());
            if (postMap.containsKey(postDTO.getName())) {
                updateList.add(postWeCom);
                return;
            }
            saveList.add(postWeCom);
        });
        syncPostRecordService.saveBatch(saveList);
        syncPostRecordService.updateBatchById(updateList);
        log.info("---- 保存更新岗位表数据成功 ----，共 {} 条。 ----", list.size());
        return list;
    }

    /**
     * 转换用户数据
     *
     * @return {@link List }<{@link SyncUserDTO }>
     */
    private List<SyncUserDTO> convertUserData() {
        return hrmUserService.querySyncUserDTO();
    }

    /**
     * 转换组织数据
     *
     * @param bindOrg 绑定组织
     * @return {@link List }<{@link SyncOrgDTO }>
     */
    private List<SyncOrgDTO> convertOrgData(OrgDTO bindOrg) {
        List<SyncOrgDTO> queryList = hrmOrgService.querySyncOrgDTO();
        Optional<SyncOrgDTO> rootOrg = queryList.stream()
            .filter(l -> StringUtils.isEmpty(l.getParentCode()) || StringUtils.equals(l.getParentCode(), "-1"))
            .findFirst();
        // 返回数据列表处理
        Set<String> orgCodes = queryList.stream().map(SyncOrgDTO::getOrgCode).collect(Collectors.toSet());
        String rootOrgCode = rootOrg.map(SyncOrgDTO::getOrgCode).orElse("0");
        queryList.forEach(syncOrgDTO -> {
            // 当前组织是否企业微信根节点组织
            boolean isRoot = rootOrgCode.equals(syncOrgDTO.getOrgCode());
            // 父级组织是否企业微信根节点组织
            boolean parentIsRoot = rootOrgCode.equals(syncOrgDTO.getParentCode());
            // 父级组织不在本次同步范围
            boolean parentNotIn = isParentNotIn(syncOrgDTO, orgCodes);
            // 如果之前挂载在根组织下面/找不到根节点的组织，直接挂载到指定的二级组织里面
            if (parentIsRoot || parentNotIn) {
                syncOrgDTO.setParentCode(bindOrg.getOrgCode());
            }
            // 企微接口获取的根组织父级id是0，所以要做特殊处理
            if (isRoot) {
                String parentCode = Optional.ofNullable(bindOrg.getParentId())
                    .map(parentId -> Optional.ofNullable(orgFeign.getById(parentId)).map(OrgDTO::getOrgCode).orElse(""))
                    .orElse("");
                // 获取父类组织编码
                syncOrgDTO.setParentCode(parentCode);
                syncOrgDTO.setOrgCode(bindOrg.getOrgCode());
//                syncOrgDTO.setOrgName(bindOrg.getOrgName())
            }
        });
        return queryList;
    }

    // 添加查询方法
    public List<Map<String, Object>> query(String sql) {
        // 简单的SQL解析，支持基本的SELECT和WHERE
        // 示例: SELECT column1, column2 FROM table WHERE column1 = value
        String[] parts = sql.split("\\s+");
        if (parts.length < 4 || !parts[0].equalsIgnoreCase("SELECT") || !parts[2].equalsIgnoreCase("FROM")) {
            throw new IllegalArgumentException("Invalid SQL query");
        }

        String[] columns = parts[1].split(",");
        String tableName = parts[3];
        String whereClause = null;
        if (parts.length > 5 && parts[4].equalsIgnoreCase("WHERE")) {
            whereClause = String.join(" ", Arrays.copyOfRange(parts, 5, parts.length));
        }

        DataTable table = dataTables.get(tableName);
        if (table == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }

        return table.select(columns, whereClause);
    }

    // 解析HRM_USER的dat文件
    private void parseHrmUserData(String content, Boolean useDataBase) {
        log.info("开始解析人员数据 HRM_USER...");
        boolean debugEnabled = log.isDebugEnabled();
        if (debugEnabled) {
            log.debug("数据内容HRM_USER:{}", content);
        }
        parseContent(content, "hrm_user", headerMap.get(HRM_USER), useDataBase);
        if (debugEnabled) {
            log.debug("解析组织单元数据 HRM_USER 完成");
        }
    }

    private void parseHrmOrgData(String content, Boolean useDataBase) {
        log.info("开始解析人员数据 HRM_ORG...");
        boolean debugEnabled = log.isDebugEnabled();
        if (debugEnabled) {
            log.debug("数据内容HRM_ORG:{}", content);
        }
        parseContent(content, "hrm_org", headerMap.get(HRM_ORG), useDataBase);
        if (debugEnabled) {
            log.debug("解析组织单元数据 HRM_ORG 完成");
        }
    }

    // 修改解析方法
//    private void parseOrgJobData(String content, Boolean useDataBase) {
//        log.info("开始解析岗位数据 D_HRM_TB_ORG_JOB...");
//        boolean debugEnabled = log.isDebugEnabled();
//        if (debugEnabled) {
//            log.debug("数据内容D_HRM_TB_ORG_JOB:{}", content);
//        }
//        parseContent(content, ORG_JOB_TABLE, headerMap.get(ORG_JOB_TABLE), useDataBase);
//        if (debugEnabled) {
//            log.debug("解析组织单元数据 OrgUnitData 完成");
//        }
//    }
//
//    private void parseOrgUnitData(String content, Boolean useDataBase) {
//        // 处理 D_HRM_TB_ORG_ORGUNIT 类型的数据
//        log.info("解析组织单元数据 D_HRM_TB_ORG_ORGUNIT...");
//        boolean debugEnabled = log.isDebugEnabled();
//        if (debugEnabled) {
//            log.debug("数据内容D_HRM_TB_ORG_ORGUNIT:{}", content);
//        }
//        // 解析数据,并将数据放置到DataTable对象中
//        parseContent(content, ORG_UNIT_TABLE, headerMap.get(ORG_UNIT_TABLE), useDataBase);
//        if (debugEnabled) {
//            log.debug("解析组织单元数据 D_HRM_TB_ORG_ORGUNIT 完成");
//        }
//    }
//
//    private void parseOrgRelationData(String content, Boolean useDataBase) {
//        // 处理 D_HRM_TB_ORG_UNITRELATION_NEW 类型的数据
//        log.info("解析组织关系数据D_HRM_TB_ORG_UNITRELATION_NEW...");
//        boolean debugEnabled = log.isDebugEnabled();
//        if (debugEnabled) {
//            log.debug("数据内容D_HRM_TB_ORG_UNITRELATION_NEW:{}", content);
//        }
//        // 解析数据,并将数据放置到DataTable对象中
//        parseContent(content, ORG_UNITRELATION_NEW_TABLE, headerMap.get(ORG_UNITRELATION_NEW_TABLE), useDataBase);
//        if (debugEnabled) {
//            log.debug("解析组织单元数据 D_HRM_TB_ORG_UNITRELATION_NEW 完成");
//        }
//    }
//
//
//    private void parseEmpOrgData(String content, Boolean useDataBase) {
//        // 处理 D_HRM_TB_STA_EMP_ORG 类型的数据
//        log.info("解析员工与组织关系数据D_HRM_TB_STA_EMP_ORG...");
//        boolean debugEnabled = log.isDebugEnabled();
//        if (debugEnabled) {
//            log.debug("数据内容D_HRM_TB_STA_EMP_ORG:{}", content);
//        }
//        // 解析数据,并将数据放置到DataTable对象中
//        parseContent(content, STA_EMP_ORG_TABLE, headerMap.get(STA_EMP_ORG_TABLE), useDataBase);
//        if (debugEnabled) {
//            log.debug("解析组织单元数据 D_HRM_TB_STA_EMP_ORG 完成");
//        }
//    }
//
//    private void parseEmployeeData(String content, Boolean useDataBase) {
//        // 处理 D_HRM_TB_STA_EMP 类型的数据
//        log.info("解析员工数据D_HRM_TB_STA_EMP...");
//        boolean debugEnabled = log.isDebugEnabled();
//        if (debugEnabled) {
//            log.debug("数据内容:{}", content);
//        }
//        // 解析数据,并将数据放置到DataTable对象中
//        parseContent(content, HRM_RENYUAN, headerMap.get(HRM_RENYUAN), useDataBase);
//        if (debugEnabled) {
//            log.debug("解析组织单元数据 D_HRM_TB_STA_EMP 完成");
//        }
//    }

    private void parseContent(String content, String tableName, List<String> headers, Boolean useDataBase) {
        String[] lines = content.split(StringPool.NEWLINE);
        if (lines.length == 0) {
            return;
        }

        DataTable jobTable = new DataTable(tableName, headers, jdbcTemplate);
        jobTable.setUseDatabase(useDataBase);
        jobTable.cleanTable();
        dataTables.put(tableName, jobTable);

        // 加载数据
        for (String line : lines) {
            if (line.trim().isEmpty()) {
                continue;
            }
            String[] columns = line.split("\\u0003");
            jobTable.addRow(columns);
        }
        jobTable.batchInsertToDatabase();

        // 数据加载完成后构建索引
        jobTable.buildIndexes();
        log.info("数据解析完成，共{}条记录", lines.length - 1);
    }

    /**
     * 从ddl解析表名和字段，List的第一个元素存放表名称,不能保证压缩包内一定存在表结构,这里先添加过期注解
     *
     * @param ddl ddl
     * @return {@link List }<{@link String }>
     */
    public static List<String> parseHeadersFromDDL(String ddl) {
        List<String> headers = new ArrayList<>();

        // 提取表名（支持多种引号）
        Pattern tablePattern = Pattern.compile(
            "(?i)CREATE\\s+TABLE\\s+(?:\\w+\\.)?(?:(`)([^`]+)`|\"([^\"]+)\"|$$(.*?)$$|(\\w+))",
            Pattern.MULTILINE
        );
        Matcher tableMatcher = tablePattern.matcher(ddl);
        if (tableMatcher.find()) {
            for (int i = 2; i <= 5; i++) {
                String tableName = tableMatcher.group(i);
                if (tableName != null && !tableName.isEmpty()) {
                    headers.add(tableName.trim());
                    break;
                }
            }
        }

        // 提取字段名（支持多种引号、跳过类型和约束）
        Pattern fieldPattern = Pattern.compile(
            "\"([^\"]+)\"\\s+\\w+(?:\\([^)]+\\))?(?:\\s+\\w+)*",
            Pattern.MULTILINE
        );
        Matcher fieldMatcher = fieldPattern.matcher(ddl);

        while (fieldMatcher.find()) {
            String columnName = fieldMatcher.group(1);
            headers.add(columnName);
        }

        return headers;
    }


    private static boolean isParentNotIn(SyncOrgDTO syncOrgDTO, Set<String> orgIds) {
        return StringUtils.isNotBlank(syncOrgDTO.getParentCode()) && !orgIds.contains(syncOrgDTO.getParentCode());
    }

    /**
     * 处理zip文件
     *
     * @param bufferedIn 缓冲在
     * @param txtEntries txt条目
     * @param datEntries 该 条目
     * @throws IOException IOException
     */
    private void processZipFile(BufferedInputStream bufferedIn, List<EntryContent> txtEntries,
        List<EntryContent> datEntries) throws IOException {
        try (ZipArchiveInputStream zipIn = new ZipArchiveInputStream(bufferedIn)) {
            ArchiveEntry entry;
            while ((entry = zipIn.getNextEntry()) != null) {
                // 跳过目录条目
                if (entry.isDirectory()) {
                    continue;
                }

                String entryName = entry.getName();
                if (entryName.endsWith(".txt")) {
                    String ddl = readContentWithEncodingDetection(zipIn, entryName);
                    txtEntries.add(new EntryContent(entryName, ddl));
                } else if (entryName.endsWith(".dat")) {
                    String content = readContentWithEncodingDetection(zipIn, entryName);
                    datEntries.add(new EntryContent(entryName, content));
                }
            }
        }
    }

    /**
     * 处理targz文件
     *
     * @param bufferedIn 缓冲在
     * @param txtEntries txt条目
     * @param datEntries 该 条目
     * @throws IOException IOException
     */
    private void processTarGzFile(BufferedInputStream bufferedIn, List<EntryContent> txtEntries,
        List<EntryContent> datEntries) throws IOException {
        try (InputStream gzipIn = new GzipCompressorInputStream(bufferedIn);
            TarArchiveInputStream tarIn = new TarArchiveInputStream(gzipIn)) {

            ArchiveEntry entry;
            while ((entry = tarIn.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    continue;
                }

                String entryName = entry.getName();
                if (entryName.endsWith(".txt")) {
                    String ddl = IOUtils.toString(tarIn, StandardCharsets.UTF_8);
                    txtEntries.add(new EntryContent(entryName, ddl));
                } else if (entryName.endsWith(".dat")) {
                    String content = IOUtils.toString(tarIn, StandardCharsets.UTF_8);
                    datEntries.add(new EntryContent(entryName, content));
                }
            }
        }
    }

    /**
     * 处理单个gz文件
     *
     * @param bufferedIn 缓冲在
     * @param fileName   文件名
     * @param txtEntries txt条目
     * @param datEntries 该 条目
     * @throws IOException IOException
     */
    private void processSingleGzFile(BufferedInputStream bufferedIn, String fileName, List<EntryContent> txtEntries,
        List<EntryContent> datEntries) throws IOException {
        try (InputStream gzipIn = new GzipCompressorInputStream(bufferedIn)) {
            String content = IOUtils.toString(gzipIn, StandardCharsets.UTF_8);

            // 去掉 .gz 扩展名获取原始文件名
            String originalFileName = fileName.endsWith(".gz") ?
                fileName.substring(0, fileName.length() - 3) : fileName;

            if (originalFileName.endsWith(".txt")) {
                txtEntries.add(new EntryContent(originalFileName, content));
            } else if (originalFileName.endsWith(".dat")) {
                datEntries.add(new EntryContent(originalFileName, content));
            }
        }
    }

    /**
     * 处理单个文件
     *
     * @param bufferedIn 缓冲在
     * @param fileName   文件名
     * @param txtEntries txt条目
     * @param datEntries 该 条目
     * @throws IOException IOException
     */
    private void processSingleFile(BufferedInputStream bufferedIn, String fileName, List<EntryContent> txtEntries,
        List<EntryContent> datEntries) throws IOException {
        String content = IOUtils.toString(bufferedIn, StandardCharsets.UTF_8);

        if (fileName.endsWith(".txt")) {
            txtEntries.add(new EntryContent(fileName, content));
        } else if (fileName.endsWith(".dat")) {
            datEntries.add(new EntryContent(fileName, content));
        }
    }


    // 新增编码检测和读取方法
    private String readContentWithEncodingDetection(InputStream inputStream, String fileName) throws IOException {
        // 读取字节数据
        byte[] bytes = IOUtils.toByteArray(inputStream);

        // 尝试多种编码
        Charset[] charsets = {
            StandardCharsets.UTF_8,
            Charset.forName("GBK"),
            Charset.forName("GB2312"),
            StandardCharsets.ISO_8859_1,
            Charset.forName("Windows-1252")
        };

        for (Charset charset : charsets) {
            try {
                String content = new String(bytes, charset);
                // 验证内容是否包含有效字符（简单验证）
                if (log.isDebugEnabled()) {
                    log.debug("文件 {} 使用编码 {} 读取成功", fileName, charset.name());
                }
                return content;
            } catch (Exception e) {
                log.debug("文件 {} 使用编码 {} 读取失败: {}", fileName, charset.name(), e.getMessage());
            }
        }

        // 如果所有编码都失败
        log.warn("文件 {} 无法确定正确编码", fileName);
        return new String(bytes, StandardCharsets.UTF_8);
    }


}
