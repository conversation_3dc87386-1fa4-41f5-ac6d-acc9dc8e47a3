# 应用服务 WEB 访问端口
server:
  port: 28001
# 应用名称
spring:

  # 数据库设置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: p6l7hZQqBHMe2aftwysZ

  #redis
  # Redis服务器地址
  data:
    redis:
      #host: 127.0.0.1
      host: ************
      # Redis服务器连接端口
      #port: 6379
      port: 30079
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000

  elasticsearch:
    uris: ************:30920
    username: elastic
    password: X282YZNcXIu9nH5K49k09S1K
    connectionTimeout: 5s

    #rabbitmq 配置
  rabbitmq:
    host: ************
    port: 30672
    virtual-host: /chh
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://************:30917

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://************:30915
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true




xxl:
  job:
    admin:
      addresses: http://************/xxl-job-admin
      username: admin
      password: 123456
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf

learn:
  service:
    learn-file-service: "http://************:28003"
    learn-user-es: "http://************:28005"
    learn-push-service: "http://************:28024"
    learn-websocket: "http://************:28011"
    ai-maxkb-service: "http://************:8081/api"
    resourceFeign:
      - name: course
        url: http://************:28006
      - name: info
        url: http://************:28018
      - name: special
        url: http://************:28015
      - name: special
        url: http://************:28013




#seata:
#  client:
#    undo:
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  #  enable-auto-data-source-proxy: false
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: ************:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group

#应用相关的设置
app:
  bizLogEsIndexName: biz-log
  signKey: agjmbz4vwxue3qs9oovtvnq2bezcbn8bz09w26wtd6nih6qui4g0sxp2uvgkit84u7kf76x3wka7on5s
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKsnfFIV7o9_KITb0_M4qZO1sf1bjp9Sbuiz50TGPFg_fx8Buny8WQflyjIHkO6Z6HRwhGmjoL1Kz5Eknn64pZLV7v3i988C6CHCYHSbT7rc_Xiy6z4v3bEi0WiMQC_yPQDIEVP6y07GDz6zdYkutOayJcEywcU0xXcnfth6CCpvAgMBAAECgYBsJnNEW193hV5RNadklXVyROnHsscYnbo_iQ6mQq13BgiJy0nP8CRB_U4a9vT6EH72tPK23hKACnnGuWD9qifU7JRY1Vl8Rud3wkMPmfl_ocowe43OegmuLWknjZXwuGH_z1wnxZ2rDOx4G5T2P7IapKaYaTXzjGHzph403-xZgQJBANOLpOY95sQOFQq0bhmQDJosaEXEhIEo-5TKiEdneVSyu261NGMlwdRY3rEFyteT5GJOawkj8bqm0mq8ReX4E9ECQQDPHvGRXxMLt7ZCvqIoYMry6XsDprNNxknyq6CBZOCQHwczjWKd3nfiqBONi3OJZaMYa4UL4UCVXBcZkwD4yyo_AkAYYvnAfRRUN5dfY4tpsPxy7XmbyVrJFPNjpLFvIdOP2wbWbVc7sseUdiY93AAVN_xVBNs784PTU5XgLhIUv7NxAkBAkpUdjVaSwKPCC0zi6cpcEQ6ZBM_B36AOWBOiQ6D_Ta0hFWM5dLJLp7rw1hkfLOC8LEk-eut3pU5OWtZiunRhAkBySxnRjxbHx7N6TrNvwt1DDtiiDMBRIZlrqNvnk0-aQ-qc45anyjE4-EOCC6inAVgWSGqpweBXmyN-c8Sdttn1
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrJ3xSFe6PfyiE29PzOKmTtbH9W46fUm7os-dExjxYP38fAbp8vFkH5coyB5Dumeh0cIRpo6C9Ss-RJJ5-uKWS1e794vfPAughwmB0m0-63P14sus-L92xItFojEAv8j0AyBFT-stOxg8-s3WJLrTmsiXBMsHFNMV3J37YeggqbwIDAQAB

  single:
    - api
  user:
    maxCount: 30000000  #用户最大数量设置
  password:
    default: WunDing@1996
    new: WunDing@2024

  # 企业微信服务商
  wecom:
    # 第三方应用SuiteId
    thirdAppSuiteId: wx7a6dac360b4b956c
    # 第三方应用SuiteSecret
    thirdAppSuiteSecret: 04JJBmBp3GjUY1oTcWpyNqwe3vnwY6a_8AI4m8i0sI4
operation:
  domain: console.sit.wdxuexi.com

# 广告位数量
adsImageNum: 2

############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: api
      packages-to-scan:
        - com.wunding.learn.user.service.client.rest
        - com.wunding.learn.common.form.client.rest
    - group: web
      packages-to-scan:
        - com.wunding.learn.user.service.admin.rest
        - com.wunding.learn.common.form.admin.rest
    - group: flowable-api
      packages-to-scan: com.wunding.learn.flowable.service.client.rest
    - group: flowable-web
      packages-to-scan: com.wunding.learn.flowable.service.admin.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true

#人脸识别
face:
  appId: 35784184
  apiKey: iBVaHLjs8gorN529LmTHXMjH
  secretKey: cBQrBXbap3yKBNnIIRqOulCUOXFN0RsM

