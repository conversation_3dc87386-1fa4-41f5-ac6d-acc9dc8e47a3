package com.wunding.learn.project.service.admin.dto;

import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 项目审核详细信息dto数据传输对象
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProjectProcessInfoDTO", description = "项目审核详细信息dto")
public class ProjectProcessInfoDTO {

    @Schema(description = "申请人id")
    private String applicantUserId;

    @Schema(description = "申请人账户")
    private String applicantLoginName;

    @Schema(description = "申请人name")
    private String applicantUserName;


    @Schema(description = "项目名称")
    private String proName;

    //培训项目

    @Schema(description = "关联计划Id")
    private String trainPlanId;

    @Schema(description = "关联计划名称")
    private String trainPlanName;

    @Schema(description = "培训类别名称")
    private String trainCategoryName;

    //项目对象

    @Schema(description = "教室名称")
    private String roomName;

    @Schema(description = "班主任名称")
    private String leaderName;

    @Schema(description = "计划类型 0固定日期，1固定周期")
    private Integer type;

    @Schema(description = "固定周期天数")
    private Long cycleDay;

    @Schema(description = "固定日期开始时间")
    private Date startTime;

    @Schema(description = "固定日期结束时间")
    private Date endTime;
    //简介
    @Schema(description = "项目描述 说明")
    private String proDesc;


    @Schema(description = "备注")
    private String mark;

    @Schema(description = "地点")
    private String address;

    @Schema(description = "下发基本信息")
    private ViewLimitBaseInfoDTO limit;

    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见", required = true)
    private Integer viewType;

    @Schema(description = "创建、归属部门Id", hidden = true)
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;
}
