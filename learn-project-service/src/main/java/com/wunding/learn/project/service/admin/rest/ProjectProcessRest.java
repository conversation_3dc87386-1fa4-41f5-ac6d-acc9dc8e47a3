package com.wunding.learn.project.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.ProcessAuditDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailDTO;
import com.wunding.learn.flowable.api.dto.ProcessListBaseDTO;
import com.wunding.learn.project.service.admin.dto.ProjectProcessInfoDTO;
import com.wunding.learn.project.service.biz.ProjectProcessBiz;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("${module.project.contentPath:/}projectProcess")
@Tag(description = "项目审核管理", name = "ProjectProcessRest")
public class ProjectProcessRest {

    @Autowired
    private ProjectProcessBiz projectProcessBiz;


    @PostMapping("/getProjectProcessList")
    @Operation(operationId = "getInfoProcessList_InfoProcessRest", summary = "获取项目审核流程", description = "获取项目审核流程列表")
    public Result<PageInfo<ProcessListBaseDTO>> getInfoProcessList(@RequestBody BaseEntity query) {
        return Result.success(projectProcessBiz.getProjectProcessList(query));
    }

    @PostMapping("/auditProcess/project")
    @Operation(operationId = "auditProcess_project", summary = "项目申请处理", description = "项目申请处理")
    public Result<Void> auditProcess(@RequestBody @Validated ProcessAuditDTO processAuditDTO) {
        projectProcessBiz.auditProcess(processAuditDTO);
        return Result.success();
    }

    @GetMapping("/getProjectProcessInfo")
    @Operation(operationId = "getProjectProcessInfo", summary = "项目审核信息", description = "项目审核信息")
    public Result<ProjectProcessInfoDTO> getCourseProcessInfo(
        @Parameter(description = "项目审核的流程实例id") @RequestParam("id") String id) {
        return Result.success(projectProcessBiz.getProjectProcessInfo(id));
    }

    @PutMapping("/applyAudit")
    @Operation(operationId = "applyAudit_ProjectRest", summary = "项目提交审核", description = "项目提交审核")
    public Result<Void> applyAudit(@RequestParam("projectId") String projectId) {
        projectProcessBiz.applyAudit(projectId);
        return Result.success();
    }

    @PutMapping("/revokeProjectProcess")
    @Operation(operationId = "revokeProjectProcess_ProjectRest", summary = "撤销项目审核", description = "撤销项目审核")
    public Result<Void> revokeInfoProcess(@RequestParam("projectId") String projectId) {
        projectProcessBiz.revokeInfoProcess(projectId);
        return Result.success();
    }

    @GetMapping("/getProjectAuditStatus")
    @Operation(deprecated = true, operationId = "getProjectAuditStatus", summary = "获取项目审核状态", description = "获取项目审核状态")
    public Result<Integer> getInfoAuditStatus(
        @Parameter(description = "项目id") @RequestParam("projectId") String projectId) {
        return Result.success(projectProcessBiz.getInfoAuditStatus(projectId));
    }

    @GetMapping("/detail")
    @Operation(operationId = "detail_ProjectProcessRest", summary = "审核流程定义详情", description = "审核流程定义详情")
    public Result<ProcessDefinitionDetailDTO> detail(@RequestParam("definitionId") String definitionId) {
        // 审核流程定义详情
        return Result.success(projectProcessBiz.detail(definitionId));
    }

}
