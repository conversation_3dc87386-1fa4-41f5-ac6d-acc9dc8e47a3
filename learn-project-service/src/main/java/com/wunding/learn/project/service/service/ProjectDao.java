package com.wunding.learn.project.service.service;

import com.wunding.learn.project.service.admin.dto.ProjectDTO.ProphaseSaveDTO;
import com.wunding.learn.project.service.client.dto.ProjectJoinDTO;
import com.wunding.learn.project.service.model.Project;
import java.util.List;

/**
 * @program: mlearn
 * @description: <p>学习项目主表使用spring异步注解导致循环依赖问题，所以将其他业务层需要调用学习项目的方法写在这个类</p>
 * @author: 赖卓成
 * @create: 2022-08-23 10:32
 **/

public interface ProjectDao {

    /**
     * 该学习项目下项目阶段
     *
     * @param projectId
     * @return
     */
    List<ProphaseSaveDTO> phaseList(String projectId);

    /**
     * 项目存在 项目是否存在,若存在则返回
     *
     * @param id 项目id
     * @return {@link Project}
     */
    Project projectExist(String id);

    /**
     * 参与/退出学习项目
     *
     * @param id          id
     * @param applyStatus 报名状态
     */
    void joinOrExitProject(String id, ProjectJoinDTO joinDTO);

    /**
     * 检查资源是否在下发范围内
     *
     * @param id     id
     * @param userId 用户id
     */
    void checkProjectViewLimit(String id, String userId);
}
