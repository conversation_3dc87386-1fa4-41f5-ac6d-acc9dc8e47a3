package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.share.dto.PosterShareClientDTO;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.project.api.dto.FaceProjectApiDTO;
import com.wunding.learn.project.api.dto.ProjectApiDTO;
import com.wunding.learn.project.api.dto.ProjectDetailDTO;
import com.wunding.learn.project.api.dto.ProjectListDTO;
import com.wunding.learn.project.api.dto.ProjectProcessDTO;
import com.wunding.learn.project.api.dto.ProjectSaveDTO;
import com.wunding.learn.project.api.dto.ProjectStatisticDTO;
import com.wunding.learn.project.api.dto.ProjectUpdateDTO;
import com.wunding.learn.project.api.query.ProjectListQuery;
import com.wunding.learn.project.api.query.ProjectStatisticQueryDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminUpdateDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAndTaskInfoDTO;
import com.wunding.learn.project.service.admin.dto.ProjectCourseWarePageDTO;
import com.wunding.learn.project.service.admin.dto.ProjectManagerDTO;
import com.wunding.learn.project.service.admin.dto.ProjectStatisticUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.RemindersDTO;
import com.wunding.learn.project.service.admin.dto.UserRemindersDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.ScheduleStatDTO;
import com.wunding.learn.project.service.admin.query.LecturerQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectManagerQueryDTO;
import com.wunding.learn.project.service.admin.query.ProjectUserQuery;
import com.wunding.learn.project.service.admin.query.RemindersQuery;
import com.wunding.learn.project.service.admin.query.SelectProjectListQuery;
import com.wunding.learn.project.service.admin.query.faceproject.ScheduleStatQuery;
import com.wunding.learn.project.service.client.dto.FaceProjectPageDTO;
import com.wunding.learn.project.service.client.dto.FaceProjectUserApplyIdentity;
import com.wunding.learn.project.service.client.dto.LearnProjectDTO;
import com.wunding.learn.project.service.client.dto.MentorForm;
import com.wunding.learn.project.service.client.dto.ProjectInfoDTO;
import com.wunding.learn.project.service.client.dto.ProjectMaterialExampleFileApiDTO;
import com.wunding.learn.project.service.client.dto.ProjectPageDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectRoleDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectUserStatDTO;
import com.wunding.learn.project.service.client.dto.SubordinateProjectDTO;
import com.wunding.learn.project.service.client.dto.SuperviseProjectPageDTO;
import com.wunding.learn.project.service.client.dto.SuperviseProjectPageQuery;
import com.wunding.learn.project.service.client.dto.TrainOrgHomeDTO;
import com.wunding.learn.project.service.client.dto.UserIdpProjectDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkbenchProjectPageDTO;
import com.wunding.learn.project.service.client.query.FaceProjectPageQuery;
import com.wunding.learn.project.service.client.query.MentorNoSolveQuery;
import com.wunding.learn.project.service.client.query.ProjectHomePageQuery;
import com.wunding.learn.project.service.client.query.ProjectPageQuery;
import com.wunding.learn.project.service.client.query.ProjectSearchQuery;
import com.wunding.learn.project.service.client.query.QuickProjectUserStatQuery;
import com.wunding.learn.project.service.client.query.StudentProjectQuery;
import com.wunding.learn.project.service.client.query.SubordinateProjectQuery;
import com.wunding.learn.project.service.client.query.TrainProjectQuery;
import com.wunding.learn.project.service.client.query.UserIdpProjectQuery;
import com.wunding.learn.project.service.client.query.lecturerworkbench.LecturerProjectQuery;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.model.TaskProgress;
import com.wunding.learn.project.service.query.ProjectJoinQuery;
import com.wunding.learn.user.api.dto.viewlimit.ResourceViewLimitInfoDTO;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 项目主表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
public interface IProjectService extends IService<Project> {

    IProjectService getBean();

    /**
     * 学习项目列表
     *
     * @param projectManagerQueryDTO 项目经理查询dto
     * @return {@link PageInfo}<{@link ProjectManagerDTO}>
     */
    PageInfo<ProjectManagerDTO> queryPage(ProjectManagerQueryDTO projectManagerQueryDTO);


    /**
     * 查询被引用的学习项目
     *
     * @param projectListQuery
     * @return
     */
    PageInfo<ProjectListDTO> queryReferencedProjectPage(ProjectListQuery projectListQuery);

    /**
     * 创建学习项目
     *
     * @param projectAdminSaveDTO
     */
    Project create(ProjectAdminSaveDTO projectAdminSaveDTO, String id);

    /**
     * 保存学习项目
     *
     * @param projectSaveDTO
     */
    String saveProject(ProjectSaveDTO projectSaveDTO);

    /**
     * 更新学习项目
     *
     * @param oldProject
     * @param projectAdminUpdateDTO
     * @return
     */
    Project update(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO);

    /**
     * 更新学习项目
     *
     * @param projectUpdateDTO
     */
    void updateProject(ProjectUpdateDTO projectUpdateDTO);

    /**
     * 删除学习项目
     *
     * @param ids
     * @return
     */
    List<Project> remove(String ids);

    /**
     * 发布/取消发布学习项目
     *
     * @param publishDTO
     * @return
     */
    List<Project> publish(PublishDTO publishDTO);

    /**
     * 学习项目催办列表
     *
     * @param projectId         项目id
     * @param remindersQueryDTO 催办查询dto
     * @return {@link PageInfo}<{@link RemindersDTO}>
     */
    PageInfo<RemindersDTO> remindersList(String projectId, RemindersQuery remindersQueryDTO);

    /**
     * 学习项目催办
     *
     * @param userRemindersDTO
     * @param projectId
     */
    void userReminders(UserRemindersDTO userRemindersDTO, String projectId);

    /**
     * 获取一个学习项目
     *
     * @param id
     * @return
     */
    ProjectAdminDTO one(String id);


    /**
     * 根据学习项目ID查询学习项目
     *
     * @param projectId
     * @return
     */
    ProjectDetailDTO findProjectDetailById(String projectId);

    /**
     * 获取学习项目已参与/未参与列表
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link ProjectPageDTO}>
     */
    PageInfo<ProjectPageDTO> getProjectPage(ProjectPageQuery query);

    /**
     * 获取学习项目已参与/未参与列表
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link ProjectPageDTO}>
     */
    PageInfo<FaceProjectPageDTO> getFaceProjectPage(FaceProjectPageQuery query);

    /**
     * 将指定项目结业
     *
     * @param projectId
     */
    void completionProject(String projectId, String updateName);

    /**
     * 一键催办学习项目
     *
     * @param projectId 项目名称
     */
    void allUserReminders(String projectId);

    /**
     * 获取学习项目详情
     *
     * @param id           id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return {@link ProjectInfoDTO}
     */
    ProjectInfoDTO getProjectInfo(String id, Integer isIgnoreView);

    /**
     * 学习项目 关联项目列表
     *
     * @param projectId
     * @param baseEntity
     * @return
     */
    PageInfo<ProjectManagerDTO> relatedProjectList(String projectId, BaseEntity baseEntity);

    /**
     * 236 获取下属项目列表
     *
     * @param subordinateProjectQuery
     * @return
     */
    PageInfo<SubordinateProjectDTO> getSubordinateProject(SubordinateProjectQuery subordinateProjectQuery);

    /**
     * * idp树，按年查询项目列表
     *
     * @param userIdpProjectQuery {@link UserIdpProjectQuery}
     * @return {@link UserIdpProjectDTO}
     */
    UserIdpProjectDTO getUserIdpProjectByYear(UserIdpProjectQuery userIdpProjectQuery);

    /**
     * 导师待处理列表
     *
     * @param mentorNoSolveQuery
     * @return
     */
    PageInfo<MentorForm> getMentorNoSolveList(MentorNoSolveQuery mentorNoSolveQuery);

    /**
     * 获取学员学习项目记录列表
     *
     * @param studentProjectQuery
     * @return
     */
    PageInfo<LearnProjectDTO> getStudentProjectList(StudentProjectQuery studentProjectQuery);

    /**
     * 异步导出
     *
     * @param query
     */
    @Async
    void projectExportData(ProjectManagerQueryDTO query);

    /**
     * 搜索学习项目
     *
     * @param projectSearchQuery
     * @return
     */
    PageInfo<ProjectPageDTO> searchProject(ProjectSearchQuery projectSearchQuery);


    /**
     * 培训项目中的学习项目
     *
     * @param trainProjectQuery
     * @return
     */
    PageInfo<ProjectPageDTO> queryTrainProject(TrainProjectQuery trainProjectQuery);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param projectIdList
     * @return
     */
    List<String> getInvalidProjectId(Collection<String> projectIdList);

    /**
     * 查询首页学习项目列表
     *
     * @param projectHomePageQuery
     * @return
     */
    PageInfo<ProjectPageDTO> findProjectHomePageList(ProjectHomePageQuery projectHomePageQuery, String userId);

    /**
     * 初始化学习项目
     *
     * @param projectId    学习项目id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     */
    void initProject(String projectId, Integer isIgnoreView);

    /**
     * 加入学习项目
     * </p>
     * 调用场景： 1.进入项目初始化； 2.报名进入项目初始化； 3.报名审核通过初始化； 4.报名添加/导入学员初始化。
     *
     * @param userId
     * @param projectId
     */
    void joinProject(String userId, String projectId);

    /**
     * 加入学习项目-保存操作
     *
     * @param projectJoinQuery
     * @param projectId
     * @param project
     * @param userId
     * @param period
     * @param list
     */
    void joinProjectSave(ProjectJoinQuery projectJoinQuery, String projectId, Project project, String userId,
        int period,
        List<TaskProgress> list);

    /**
     * 导出日期项目列表的数据
     */
    @Async
    void projectFixedDateExportData(ProjectManagerQueryDTO query);

    /**
     * 通过学习项目id获取班主任/讲师用户id集合
     *
     * @param projectId 学习项目id
     * @return {@link Set}{@link String}
     */
    Set<String> getLeaderAndLecturerUserIdsByProjectId(String projectId);

    /**
     * 获得某项目下的课件
     *
     * @param projectId
     * @return
     */
    List<ProjectCourseWarePageDTO> getProjectLectureWare(String projectId);

    /**
     * 获得某项目下的课件 分页
     *
     * @param lecturerQuery
     * @return
     */
    PageInfo<ProjectCourseWarePageDTO> getProjectWarePage(LecturerQuery lecturerQuery);

    /***
     * 根据项目Ids获取项目
     * @param idSet
     */
    Map<String, Project> getProjectInfoMapByIdList(Set<String> idSet);

    ProjectAndTaskInfoDTO getProjectAndTaskInfoByProIdAndTaskId(String proId, String taskId);

    /**
     * 学习项目名称 模糊匹配-分页
     *
     * @param query
     * @return
     */
    PageInfo<ProjectApiDTO> selectProjectList(SelectProjectListQuery query);


    /**
     * 下发范围，用户ID 变更
     *
     * @param viewLimitChangeMap
     */
    void viewLimitChange(Integer viewType, Map<String, Long> viewLimitChangeMap);

    /**
     * 查询讲师工作台学习项目分页数据
     *
     * @param query
     * @return
     */
    PageInfo<WorkbenchProjectPageDTO> selectLecturerWorkProjectPageDTOList(LecturerProjectQuery query);

    /**
     * 获取当前项目的下发范围
     *
     * @param projectId
     * @return
     */
    ResourceViewLimitInfoDTO getProjectViewLimit(String projectId);

    /**
     * 删除项目日志
     *
     * @param project
     */
    void delLog(Project project);

    /**
     * 根据项目类型获取培训
     *
     * @param id             项目id（允许空。当参数不为空时仅返回指定项目的资源；当值为空时返回最新发布时间的培训或者上次选择的培训）
     * @param isOngoingCount 是否只返回正在进行的培训数（仅当值为1时生效）
     * @param role           角色：0、学员 1、培训师 2、管理员
     * @param projectType    项目类型
     * @return
     */
    QuickProjectDTO getQuickProject(String id, Integer isOngoingCount, Integer role, Integer projectType);

    /**
     * 获取跟进表
     *
     * @param query
     * @return
     */
    PageInfo<QuickProjectUserStatDTO> getUserStat(QuickProjectUserStatQuery query);

    /**
     * 获取用户快速培训角色列表
     *
     * @return
     */
    List<QuickProjectRoleDTO> getRole();

    /**
     * 快速培训一键催办
     *
     * @param projectId
     */
    void quickProjectAllUserReminders(String projectId);

    void updateQuickProject(String quickProjectId);

    /**
     * 兑换学习
     *
     * @param projectId
     */
    void exchangeStudy(String projectId);

    /**
     * 项目学员自动结案
     *
     * @throws Exception 异常
     */
    void projectAutoCloseCase();

    Map<String, BigDecimal> getPorjectNumber(Set<String> projectIds);

    Map<String, BigDecimal> getPorjectApplyNumber(Set<String> projectIds);

    List<ProjectAnalysisByUserDTO> stateAnalysisByUser(ProjectAnalysisByUserQuery query);

    /**
     * 获取当前项目的下发范围Id
     *
     * @param projectId
     * @return
     */
    Long getProjectViewLimitId(String projectId);

    /**
     * 获取基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    List<ResourceBaseDTO> getProjectBaseList(ResourceBaseQuery resourceBaseQuery);

    /**
     * 获取监督项目
     *
     * @param query
     * @return
     */
    PageInfo<SuperviseProjectPageDTO> superviseProjectList(SuperviseProjectPageQuery query);

    /**
     * 用户首页培训机构
     *
     * @param projectHomePageQuery
     * @param userId
     * @return
     */
    PageInfo<TrainOrgHomeDTO> findTrainOrgHomePageList(ProjectHomePageQuery projectHomePageQuery, String userId);

    /**
     * 取用户在面授项目中的报名身份
     *
     * @return
     */
    FaceProjectUserApplyIdentity getFaceProjectUserApplyIdentity();

    /**
     * 取示例文件
     *
     * @param id
     * @param fileBizType
     * @return
     */
    ProjectMaterialExampleFileApiDTO findExampleFile(String id, FileBizType fileBizType);

    /**
     * 获取用户面授项目信息
     *
     * @param categoryId    项目id
     * @param currentUserId 用户id
     * @return
     */
    Map<String, FaceProjectApiDTO> getFaceProjectId(Collection<String> categoryId, String currentUserId);

    /**
     * 面授日程统计 -- 通过查询获取用户列表
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link ScheduleStatDTO}>
     */
    PageInfo<ScheduleStatDTO> getUserListByQuery(ScheduleStatQuery query);

    /**
     * 获取项目上传材料提示
     *
     * @param id
     * @return
     */
    String getUploadFileRemark(String id);

    /**
     * 通过学习项目id获取学习项目信息
     *
     * @param id 学习项目id
     * @return {@link Project}
     */
    Project get(String id);

    /**
     * 校验学习项目
     *
     * @param projectId    学习项目id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     */
    void checkProjectValid(String projectId, Integer isIgnoreView);

    /**
     * 学习项目用户学习旧数据同步
     *
     * @param targetId 项目id, 多个id时用英文逗号分隔
     */
    void projectUserStudyConditionOldDate(String targetId);

    /**
     * 获得学习项目海报分享按钮状态
     *
     * @param id
     * @return
     */
    boolean getPosterShareStatus(String id);

    /**
     * 获取学习项目海报分享信息
     *
     * @param id
     * @return
     */
    PosterShareClientDTO getPosterShare(String id);

    /**
     * 获取导师待处理
     *
     * @param currentUserId 当前导师用户id
     * @param taskRecordId  辅导任务记录id
     * @param taskContentId 辅导任务内容id
     * @return {@link MentorForm}
     */
    MentorForm getMentorNoSolve(String currentUserId, String taskRecordId, String taskContentId);

    /**
     * 统计项目信息列表根据给定的查询条件
     *
     * @param projectStatisticQueryDTO 一个包含查询条件的对象，用于指定统计的范围和标准
     * @return 返回一个分页的项目统计信息列表，其中每个元素都是一个ProjectStatisticDTO对象，包含了具体的统计信息
     */
    PageInfo<ProjectStatisticDTO> statisticList(ProjectStatisticQueryDTO projectStatisticQueryDTO);

    /**
     * 导出项目统计列表的数据
     *
     * @param query 一个包含查询条件的对象，用于指定统计的范围和标准
     */
    @Async("exportTaskThreadPool")
    void statisticExport(ProjectStatisticQueryDTO query);

    PageInfo<ProjectStatisticUserDetailDTO> getProjectUser(ProjectUserQuery query);

    /**
     * 导出项目的学员列表
     *
     * @param query
     */
    @Async("exportTaskThreadPool")
    void exportProjectUser(ProjectUserQuery query);

    /**
     * 项目导入学员
     *
     * @param excelFilePath 导入文件路径
     * @return 导入结果
     */
    Future<ImportResultDTO> importProjectUserData(
        @NotBlank(message = "导入excel文件路径不能为空") String excelFilePath);

    /**
     * 项目导入讲师
     *
     * @param excelFilePath 导入文件路径
     * @return 导入结果
     */
    Future<ImportResultDTO> importProjectLecturerData(
        @NotBlank(message = "导入excel文件路径不能为空") String excelFilePath);

    /**
     * 获取项目的辅导任务的所有辅导模版的所有栏目的所有导师类型
     */
    List<String> getProjectAllMentorCateIds(String projectId);
    
    /**
     * 获取项目审核流程信息
     *
     * @param projectId
     * @return
     */
    ProjectProcessDTO findProjectProcessById(String projectId);
}
