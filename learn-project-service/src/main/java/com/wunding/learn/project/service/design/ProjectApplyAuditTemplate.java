package com.wunding.learn.project.service.design;

import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.flowable.api.design.template.ApplyAuditTemplate;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.flowable.api.service.IProcessInstanceTaskResourceService;
import com.wunding.learn.project.service.mapper.ProjectMapper;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.service.ProjectDao;
import com.wunding.learn.user.api.service.UserFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component("projectApplyAuditTemplate")
@Slf4j
public class ProjectApplyAuditTemplate extends ApplyAuditTemplate<Project> {


    protected final ProjectMapper projectMapper;


    public ProjectApplyAuditTemplate(ProjectDao projectDao,
        ProjectMapper projectMapper, IProcessInstanceResourceService processInstanceResourceService,
        IProcessInstanceTaskResourceService processInstanceTaskResourceService,
        ICategorysService categorysService,
        RedisTemplate<String, Object> redisTemplate,
        UserFeign userFeign,
        ProcessFeign processFeign) {
        this.projectMapper = projectMapper;
        super.processFeign = processFeign;
        super.processInstanceResourceService = processInstanceResourceService;
        super.processInstanceTaskResourceService = processInstanceTaskResourceService;
        super.categorysService = categorysService;
        super.redisTemplate = redisTemplate;
        super.userFeign = userFeign;
    }

    @Override
    protected void updateResource(Project resource) {
        projectMapper.updateById(resource);
    }

    @Override
    protected Project getResource(String resourceId) {
        return projectMapper.selectById(resourceId);
    }
}
