package com.wunding.learn.project.service.biz.impl;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.dto.ProcessAuditDTO;
import com.wunding.learn.common.dto.ResourceProcessAuditDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.process.AuditOperateEnum;
import com.wunding.learn.common.enums.process.ProcessDefinitionTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.flowable.api.constant.AuditStatusEnum;
import com.wunding.learn.flowable.api.design.template.ApplyAuditTemplate;
import com.wunding.learn.flowable.api.design.template.AuditTaskTemplate;
import com.wunding.learn.flowable.api.dto.CompleteOperateParamDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailDTO;
import com.wunding.learn.flowable.api.dto.ProcessInstanceResourceFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessListBaseDTO;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.query.ProcessQueryDTO;
import com.wunding.learn.project.api.dto.ProjectProcessDTO;
import com.wunding.learn.project.service.admin.dto.ProjectProcessInfoDTO;
import com.wunding.learn.project.service.biz.ProjectProcessBiz;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service("projectProcessBiz")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProjectProcessBizImpl implements ProjectProcessBiz {

    private final UserFeign userFeign;
    private final ApplyAuditTemplate<Project> projectApplyAuditTemplate;
    private final IProjectService projectService;
    private final AuditTaskTemplate<CompleteOperateParamDTO> projectAuditTaskTemplate;
    private final ProcessFeign processFeign;

    @Override
    public PageInfo<ProcessListBaseDTO> getProjectProcessList(BaseEntity query) {
        ProcessQueryDTO processQueryDTO = new ProcessQueryDTO();
        BeanUtils.copyProperties(query, processQueryDTO);
        processQueryDTO.setProcessType(ProcessDefinitionTypeEnum.PROJECT_AUDIT.getType());
        return projectApplyAuditTemplate.getProcessList(processQueryDTO);
    }

    @Override
    public void auditProcess(ProcessAuditDTO processAuditDTO) {
        if (processAuditDTO.getResourceProcessAuditDTOList().isEmpty()) {
            throw new BusinessException(ErrorNoEnum.ERR_NONULL);
        }
        List<String> projcetIdList = processAuditDTO.getResourceProcessAuditDTOList().stream()
            .map(ResourceProcessAuditDTO::getResourceId).collect(
                Collectors.toUnmodifiableList());
        List<Project> projectList = projectService.listByIds(projcetIdList);
        Map<String, Project> projectMap = projectList.stream()
            .collect(Collectors.toMap(Project::getId, function -> function, (key, key2) -> key));
        String remark = processAuditDTO.getRemark();
        Integer status = processAuditDTO.getStatus();
        AuditOperateEnum auditOperateEnum = AuditStatusEnum.transAuditStatusToAuditOperateEnum(status);
        processAuditDTO.getResourceProcessAuditDTOList().forEach(
            dto -> {
                CompleteOperateParamDTO completeOperateParamDTO = new CompleteOperateParamDTO();
                completeOperateParamDTO.setAuditOperate(auditOperateEnum.getOperate());
                completeOperateParamDTO.setResourceId(dto.getResourceId());
                completeOperateParamDTO.setResourceType(ResourceTypeEnum.PROJECT.getType());
                completeOperateParamDTO.setTaskId(dto.getTaskId());
                completeOperateParamDTO.setComments(remark);
                completeOperateParamDTO.setApplicantUserId(projectMap.get(dto.getResourceId()).getCreateBy());
                projectAuditTaskTemplate.processResult(completeOperateParamDTO);
            }
        );
    }

    @Override
    public void applyAudit(String projectId) {
        projectApplyAuditTemplate.applyAudit(projectId, ProcessDefinitionTypeEnum.PROJECT_AUDIT);
    }

    @Override
    public void revokeInfoProcess(String projectId) {
        projectApplyAuditTemplate.cancelAudit(projectId);
    }

    @Override
    public Integer getInfoAuditStatus(String projectId) {
        return projectApplyAuditTemplate.getResourceAuditStatus(projectId);
    }

    @Override
    public ProcessDefinitionDetailDTO detail(String definitionId) {
        return projectApplyAuditTemplate.detail(definitionId);
    }

    @Override
    public ProjectProcessInfoDTO getProjectProcessInfo(String id) {
        ProjectProcessInfoDTO projectProcessInfoDTO = new ProjectProcessInfoDTO();
        ProcessInstanceResourceFeignDTO processInstanceResource = processFeign.getProcessInstanceById(id);
        log.info("processInstanceResource: {}", processInstanceResource);
        ProjectProcessDTO projectProcessDTO = projectService.findProjectProcessById(
            processInstanceResource.getResourceId());
        BeanUtils.copyProperties(projectProcessDTO, projectProcessInfoDTO);
        projectProcessInfoDTO.setProName(projectProcessDTO.getProName());                      //项目名称
        projectProcessInfoDTO.setTrainCategoryName(projectProcessDTO.getTrainPlanName());        //培训计划分类名称
        projectProcessInfoDTO.setRoomName(projectProcessDTO.getRoomName());                    //教室名称
        projectProcessInfoDTO.setStartTime(projectProcessDTO.getStartTime());                  //项目开始时间
        projectProcessInfoDTO.setEndTime(projectProcessDTO.getEndTime());                      //项目结束时间
        projectProcessInfoDTO.setCycleDay(projectProcessDTO.getCycleDay());                     //项目周期
        projectProcessInfoDTO.setProDesc(projectProcessDTO.getProDesc());                      //项目简介
        projectProcessInfoDTO.setMark(projectProcessDTO.getMark());                            //备注
        projectProcessInfoDTO.setLeaderName(projectProcessDTO.getLeaderName());                 //班主任
        projectProcessInfoDTO.setAddress(projectProcessDTO.getAddress());                      //地点
        projectProcessInfoDTO.setLimit(projectProcessDTO.getLimit());
        projectProcessInfoDTO.setViewType(projectProcessDTO.getViewType());
        projectProcessInfoDTO.setOrgId(projectProcessDTO.getOrgId());
        projectProcessInfoDTO.setOrgName(projectProcessDTO.getOrgName());
        log.info("projectProcessInfoDTO： {}", projectProcessInfoDTO);
        UserDTO userDTO = userFeign.getUserById(processInstanceResource.getApplicantUserId());
        projectProcessInfoDTO.setApplicantUserId(userDTO.getId());
        projectProcessInfoDTO.setApplicantLoginName(userDTO.getLoginName());
        projectProcessInfoDTO.setApplicantUserName(userDTO.getFullName());
        return projectProcessInfoDTO;
    }

}
