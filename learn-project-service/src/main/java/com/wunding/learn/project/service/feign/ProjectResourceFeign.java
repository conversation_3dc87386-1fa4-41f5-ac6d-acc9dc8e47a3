package com.wunding.learn.project.service.feign;

import com.google.common.collect.Maps;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.flowable.api.dto.ResourceInfoDTO;
import com.wunding.learn.flowable.api.feign.ResourceFeign;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.service.IProjectService;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("${module.project.contentPath:/}")
public class ProjectResourceFeign implements ResourceFeign {

    private final IProjectService projectService;
    private final ICategorysService categorysService;


    @Override
    public Map<String, ResourceInfoDTO> getResourceInfo(Collection<String> resourceIdList) {
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return Collections.emptyMap();
        }
        List<Project> projectList = projectService.listByIds(resourceIdList);
        if (CollectionUtils.isEmpty(projectList)) {
            return Collections.emptyMap();
        }

        Map<String, ResourceInfoDTO> resultMap = Maps.newHashMap();
        List<String> categroyIdList = projectList.stream().map(Project::getCategoryId).toList();
        Map<String, Categorys> categorysMap = categorysService.listByIds(categroyIdList).stream()
            .collect(Collectors.toMap(Categorys::getId, Function.identity(), (key1, key2) -> key1));
        Categorys emptyCategory = new Categorys();

        for (Project project : projectList) {
            ResourceInfoDTO resourceInfoDTO = new ResourceInfoDTO();
            resourceInfoDTO.setResourceId(project.getId());
            resourceInfoDTO.setResourceName(project.getReferencedName());
            resourceInfoDTO.setResourceCode(project.getProNo());
            resourceInfoDTO.setAuditStatus(project.getAuditStatus());
            // 映射审核状态 及 申请类型
            if (Objects.equals(project.getAuditStatus(), 5)) {
                resourceInfoDTO.setAuditStatusName("不涉及");
            } else if (Objects.equals(project.getAuditStatus(), 4)) {
                resourceInfoDTO.setAuditStatusName("驳回");
            } else if (Objects.equals(project.getAuditStatus(), 3)) {
                resourceInfoDTO.setAuditStatusName("审批不通过");
            } else if (Objects.equals(project.getAuditStatus(), 2)) {
                resourceInfoDTO.setAuditStatusName("审批通过");
            } else if (Objects.equals(project.getAuditStatus(), 1)) {
                resourceInfoDTO.setAuditStatusName("审批中");
            } else {
                resourceInfoDTO.setAuditStatusName("草稿");
            }
            String summaryBuild;
            if (project.getType() == 1) {
                summaryBuild = "时间：" + DateUtil.formatDate(project.getStartTime()) + " 至 " + DateUtil.formatDate(
                    project.getEndTime()) + "/";
            } else {
                summaryBuild = "周期天数：" + project.getCycleDay() + "天/";
            }
            summaryBuild +=
                "分类: " + categorysMap.getOrDefault(project.getCategoryId(), emptyCategory).getCategoryName();
            resourceInfoDTO.setSummary(summaryBuild);
            resultMap.put(project.getId(), resourceInfoDTO);
        }

        return resultMap;
    }
}
