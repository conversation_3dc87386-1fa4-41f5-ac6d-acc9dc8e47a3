package com.wunding.learn.project.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.project.service.admin.dto.ProjectDTO.ProphaseSaveDTO;
import com.wunding.learn.project.service.client.dto.ProjectJoinDTO;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.enums.ApplyTypeEnum;
import com.wunding.learn.project.service.mapper.PhaseMapper;
import com.wunding.learn.project.service.mapper.ProjectMapper;
import com.wunding.learn.project.service.model.Apply;
import com.wunding.learn.project.service.model.Phase;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.service.IApplyService;
import com.wunding.learn.project.service.service.IApplyUserService;
import com.wunding.learn.project.service.service.ProjectDao;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @program: mlearn
 * @description: <p></p>
 * @author: 赖卓成
 * @create: 2022-08-23 10:36
 **/
@Service("projectDao")
public class ProjectDaoImpl implements ProjectDao {

    @Resource
    private PhaseMapper phaseMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource(name = "projectApplyService")
    private IApplyService applyService;
    @Resource
    @Lazy
    private IApplyUserService applyUserService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private MqProducer mqProducer;


    @Override
    public List<ProphaseSaveDTO> phaseList(String projectId) {
        //项目阶段
        List<Phase> phaseList = phaseMapper.selectList(
            new LambdaQueryWrapper<Phase>().eq(Phase::getProId, projectId).orderByAsc(Phase::getSortNo));
        List<ProphaseSaveDTO> prophaseSaveDTOList = new ArrayList<>();
        for (Phase phase : phaseList) {
            ProphaseSaveDTO prophaseSaveDTO = new ProphaseSaveDTO();
            prophaseSaveDTO.setId(phase.getId());
            prophaseSaveDTO.setName(phase.getPhaseName());
            prophaseSaveDTO.setSort(phase.getSortNo());
            prophaseSaveDTOList.add(prophaseSaveDTO);
        }
        return prophaseSaveDTOList;
    }

    @Override
    public Project projectExist(String id) {
        Project project = projectMapper.selectById(id);
        if (Optional.ofNullable(project).isEmpty()) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        return project;
    }


    @Override
    public void joinOrExitProject(String id, ProjectJoinDTO dto) {
        Integer applyStatus = dto.getApply();
        Integer isIgnoreView = dto.getIsIgnoreView();
        String userId = UserThreadContext.getUserId();
        // 学习项目信息
        Project project = this.projectExist(id);
        // 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            checkProjectViewLimit(id, userId);
        }
        // 检查是否有启用报名设置
        Apply apply = applyService.getByProjectId(id);
        if (null == apply || ProjectConstant.IS_NOT_AVAILABLE == apply.getIsAvailable()
            || PublishEnum.NOT_PUBLISH.getValue() == apply.getIsPublish()) {
            throw new BusinessException(ProjectErrorNoEnum.APPLY_NOT_EXIST);
        }
        // 报名
        if (applyStatus.equals(ProjectConstant.APPLY_STATUS_APPLY)) {
            // 校验报名时间
            validProjectApplyDate(apply);
            // 报名
            if (Objects.equals(dto.getApplyType(), ApplyTypeEnum.AGENT.getType())) {
                // 代报名
                checkPhone(dto.getPhone());
                applyUserService.agentApply(apply.getId(), dto.getPhone(), userId);
            } else {
                applyUserService.apply(apply.getId(), userId);
            }

            // 发送消息 [完成学习项目报名]
            mqProducer.send(new ExcitationMQEvent(
                new ExcitationMQEventDTO(userId, ExcitationEventEnum.finishProjectApply.name(), id,
                    ExcitationEventCategoryEnum.PROJECT.getCode()).setTargetName(project.getProName())
                    .setBizId(dto.getBizId()).setBizType(dto.getBizType()).setIsExchange(dto.getIsExchange())));
        }
        // 取消报名
        if (applyStatus.equals(ProjectConstant.APPLY_STATUS_CANCEL_APPLY)) {
            if (Objects.equals(dto.getApplyType(), ApplyTypeEnum.AGENT.getType())) {
                checkPhone(dto.getPhone());
                applyUserService.agentCancelApply(apply.getId(), dto.getPhone(), userId);
            } else {
                applyUserService.cancelApply(apply.getId(), userId);
            }
        }
    }

    private void checkPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            throw new BusinessException(ProjectErrorNoEnum.ERR_NO_PHONE);
        }
    }

    @Override
    public void checkProjectViewLimit(String id, String userId) {
        boolean b = resourceViewLimitService.checkViewLimit(id, LimitTable.ProjectViewLimit.name(), userId);
        if (!b) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }


    private void validProjectApplyDate(Apply apply) {
        if (null != apply) {
            Date startTime = apply.getStartTime();
            Date endTime = apply.getEndTime();
            if (startTime.compareTo(new Date()) > 0) {
                throw new BusinessException(ProjectErrorNoEnum.APPLY_NOT_START);
            }
            if (endTime.compareTo(new Date()) < 0) {
                throw new BusinessException(ProjectErrorNoEnum.APPLY_END);
            }
        }
    }
}
