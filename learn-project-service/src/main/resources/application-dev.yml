# 应用服务 WEB 访问端口
server:
  port: 28013
# 应用名称
spring:
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

  # 数据库设置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: p6l7hZQqBHMe2aftwysZ
  #   url: *******************************************************************************************
  #   username: root
  #   password: 123456
  #redis
  # Redis服务器地址
  data:
    redis:
      #host: 127.0.0.1
      host: ************
      # Redis服务器连接端口
      #port: 6379
      port: 30079
      # Redis数据库索引（默认为0）
      database: 15
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000
  #rabbitmq 配置
  rabbitmq:
    host: ************
    port: 30672
    virtual-host: /chh
    username: guest
    password: guest

management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://************:30917

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://************:30915
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

xxl:
  job:
    admin:
      addresses: http://************/xxl-job-admin
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf

learn:
  service:
    learn-file-service: "http://************:28003"
    learn-user-service: "http://localhost:28001"
    learn-live-service: "http://************:28009"
    learn-exam-service: "http://************:28004"
    learn-survey-service: "http://************:28008"
    learn-course-service: "http://************:28006"
    learn-forum-service: "http://************:28012"
    learn-lecturer-service: "http://************:28007"
    learn-push-service: "http://************:28024"
    learn-market-service: "http://************:28028"
    learn-evaluation-service: "http://************:28029"
    learn-special-service: "http://************:28015"
    learn-excitation-service: "http://************:28014"
    learn-plan-service: "http://************:28032"
    learn-flowable-service: "http://localhost:28001"


#seata:
#  client:
#    undo:
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  enable-auto-data-source-proxy: true
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: ************:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group

app:
  signKey: agjmbz4vwxue3qs9oovtvnq2bezcbn8bz09w26wtd6nih6qui4g0sxp2uvgkit84u7kf76x3wka7on5s
  single:
    - api

############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: api
      packages-to-scan: com.wunding.learn.project.service.client.rest
    - group: web
      packages-to-scan: com.wunding.learn.project.service.admin.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true


