package com.wunding.learn.common.constant.other;


import com.wunding.learn.common.exception.ErrorNoInterface;

/**
 * 系统基础错误码 错误码控制在3位数以内
 *
 * <AUTHOR> 这里的错误码不要在用负数，一般情况不要用这个枚举类，错误消息用各自模块的枚举类
 */
public enum ErrorNoEnum implements ErrorNoInterface {

    /**
     * 正常返回
     */
    SUCCESS(0, "操作成功"),

    /**
     * 服务器内部错误
     */
    ERR_SERVER(-1, "服务器内部错误"),

    /**
     * 客户端版本太低
     */
    ERR_VERSION(-3, "客户端版本太低"),

    /**
     * 公共参数错误
     */
    ERR_COMMON_PARAMS(-6, "公共参数错误"),

    /**
     * 不允许重复提交
     */
    ERR_REPEAT_SUBMIT(409, "不允许重复提交"),
    /**
     * 相关参数错误
     */
    ERR_PARAMS(-8, "内容填写不正确"),
    ERR_NONULL(-10, "填写内容不能为空"),
    /**
     * 存在敏感词
     */
    ERR_SENSITIVE_WORDS(-42, "存在敏感词"),
    /**
     * 不允许被修改
     */
    ERR_NOT_ALLOW_MODIFY(-9, "不允许被修改"),

    /**
     * 不允许重复操作
     */
    ERR_REPEAT_OPERATE(-14, "不允许重复操作"),
    /**
     * 此二维码已失效
     */
    ERR_QR_CODE(-15, "二维码已失效，请刷新二维码"),
    /**
     * 状态已经改变
     */
    ERR_IS_DONE(-16, "状态已经改变"),
    /**
     * 无权限查看
     */
    ERR_NO_POWER(-17, "无权限查看"),

    /**
     * 加群失败
     */
    ERR_JOIN_FAIL(-20, "加群失败"),
    /**
     * 创建群失败
     */
    ERR_GROUP_INEXISTENCE(-21, "创建群失败"),

    /**
     * 不在允许时间内操作
     */
    ERR_NOT_ALLOW_OPERATE(-25, "不在允许时间内操作"),

    /**
     * 不存在
     */
    ERR_NOT_EXISTS(-26, "不存在"),

    /**
     *
     */
    ERR_DELETE_WITH_CHILD(-27, "含有下级分类，无法删除"),

    UN_AUTHORIZED(401, "请求未授权"),

    ERR_REMOTE_CALL(510, "远程服务错误"),

    ERR_IMPORT_FILE_NOT_SUPPORT(-28, "不支持该类型的文件导入"),

    ERR_IMPORT_ERROR(-29, "导入错误，请下载正确的导入模板"),

    ERR_DATE_PARAM_ERROR(-127, "日期参数错误"),

    ERR_IMPORT_USER_VIEW_ERROR(-30, "请导入正确的下发范围文件"),

    ERR_CATEGORY_LEVEL(-31, "层级必须小于4级"),

    ERR_NOT_MANAGER(-54, "用户不是管理员"),

    ERR_ORG_NOT_IN_MANAGE_AREA(-55, "部门不在管辖范围中"),

    ERR_MANAGER_AREA_EMPTY(-56, "管辖范围不能为空"),

    ERR_NO_VIEW_LIMIT(-57, "不在下发范围"),

    ERR_NO_IMPORT_DATA(-58, "没有导入数据"),

    ERR_IMPORT_DATA_TO_MANAY(-59, "导入数据过多"),

    ERR_IMPORT_SYSTEM_ERROR(-60, "系统导入错误"),

    ERR_CANT_CHANGE_USER_WORK_TYPE(-61, "该用户不能修改工种信息"),

    ERR_WORK_TYPE_NAME_ERROR(-62, "工种名称的格式不正确,正确例子:/大类/小类/工种名称/"),

    ERR_WORK_TYPE_CODE_ERROR(-63, "工种编码的格式不正确,正确例子:/大类编码/小类编码/工种编码/"),

    ERR_WORK_TYPE_EXITS(-64, "工种的编码已经存在"),

    ERR_WORK_TYPE_CANT_CHANGE(-65, "工种的编码不能进行修改"),

    ERR_WORK_TYPE_CODE_NOT_NULL(-66, "工种的编码不能为空"),

    ERR_WORK_TYPE_CODE_CONFLICT(-67, "工种的编码与已有的身份冲突"),

    ERR_WORK_TYPE_CODE_TOO_LONG(-68, "工种的编码长度不能超过36"),

    ERR_NOT_POWER_AUDIT(-69, "没有审核权限"),

    ERR_START_TIME_AFTER_END_TIME(-201, "开始时间不能大于结束时间"),

    ERR_USER_NULL(-3019, "导入的用户在用户列表中不存在"),

    ERR_USER_NOT_EQUAL_FULL_NAME(-3020, "账号与姓名不匹配!。"),

    ERR_RESOURCE_NOT_EXIST(-3031, "资源不存在或已删除"),

    ERR_RESOURCE_NOT_ENABLE(-3032, "资源未启用"),

    ERR_FILE_NOT_EXIST(-3033, "文件不存在"),

    ERR_IS_NO_PUBLISH(-3034, "资源未发布"),

    ERR_VIEW_LIMIT_USER_BAK_NOT_EXIST(-3035, "资源用户可见方案备份数据不存在"),

    ERR_RESOURCE_BAK_NOT_EXIST(-3036, "资源备份数据不存在"),

    ERR_REDIS_DATA_NOT_EXIST(-3037, "redis中的数据不存在"),

    ERR_REDIS_DATA_EXPIRED(-3038, "redis中的数据已过期"),

    ERR_CATEGORY_PARENT_NOT_AVAILABLE(19019, "上级分类未启用,无法修改"),

    ERR_VIEW_LIMIT(-57, "下发范围越权,请重新确认下发范围!"),

    ERR_TYPE_CONVERSION_FAILED(505, "类型转换失败"),

    CLIENT_ABORT_EXCEPTION(506, "客户端异常"),

    ;

    private final int errorCode;

    private final String message;

    ErrorNoEnum(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
