package com.wunding.learn.special.service.service.impl;

import static com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL;
import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.comment.api.dto.CommentDetailDTO;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.project.ProjectConstant;
import com.wunding.learn.common.constant.special.SpecialErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.ResourceSyncDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.LayoutStyleEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.ResourceSyncEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.event.special.SpecialFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.redis.annotaion.RedisCacheable;
import com.wunding.learn.common.util.bean.CopyNotNullObjectUtil;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.math.NumberOperationUtils;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.dto.ViewLimitTypeDTO;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.flowable.api.constant.AuditStatusEnum;
import com.wunding.learn.flowable.api.design.template.ApplyAuditTemplate;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.special.service.admin.dto.ChooseListDTO;
import com.wunding.learn.special.service.admin.dto.SaveSpecialDTO;
import com.wunding.learn.special.service.admin.dto.SaveSpecialDTO.SpecialPhaseSaveDTO;
import com.wunding.learn.special.service.admin.dto.SpecialDTO;
import com.wunding.learn.special.service.admin.dto.SpecialDTO.ProphaseDTO;
import com.wunding.learn.special.service.admin.dto.SpecialListDTO;
import com.wunding.learn.special.service.admin.dto.SpecialTaskDetailNumDTO;
import com.wunding.learn.special.service.admin.dto.UpdateSpecialDTO;
import com.wunding.learn.special.service.admin.query.SpecialListQuery;
import com.wunding.learn.special.service.client.dto.GroupSpecialTaskDTO;
import com.wunding.learn.special.service.client.dto.HomePageSpecialDTO;
import com.wunding.learn.special.service.client.dto.IdpSpecialDTO;
import com.wunding.learn.special.service.client.dto.MonthIdpSpecialDTO;
import com.wunding.learn.special.service.client.dto.SpecialApiListDTO;
import com.wunding.learn.special.service.client.dto.SpecialCategoryClientDTO;
import com.wunding.learn.special.service.client.dto.SpecialCategoryFilterListDTO;
import com.wunding.learn.special.service.client.dto.SpecialClientDTO;
import com.wunding.learn.special.service.client.dto.SpecialDetailDTO;
import com.wunding.learn.special.service.client.dto.SpecialFilterListDTO;
import com.wunding.learn.special.service.client.dto.SpecialFilterListDTO.IdName;
import com.wunding.learn.special.service.client.dto.SpecialTaskDTO;
import com.wunding.learn.special.service.client.dto.UserIdpSpecialDTO;
import com.wunding.learn.special.service.client.dto.UserIdpSpecialVO;
import com.wunding.learn.special.service.client.query.SpecialApiListQuery;
import com.wunding.learn.special.service.client.query.SpecialHomePageQuery;
import com.wunding.learn.special.service.client.query.UserIdpSpecialQuery;
import com.wunding.learn.special.service.component.SpecialViewLimitComponent;
import com.wunding.learn.special.service.constant.SpecialConstant;
import com.wunding.learn.special.service.dao.SpecialDao;
import com.wunding.learn.special.service.enums.SpecialAppType;
import com.wunding.learn.special.service.enums.SpecialTaskTypeEnum;
import com.wunding.learn.special.service.mapper.SpecialMapper;
import com.wunding.learn.special.service.model.Special;
import com.wunding.learn.special.service.model.SpecialProgress;
import com.wunding.learn.special.service.model.SpecialStage;
import com.wunding.learn.special.service.model.SpecialTag;
import com.wunding.learn.special.service.model.SpecialTask;
import com.wunding.learn.special.service.model.SpecialViewLimit;
import com.wunding.learn.special.service.service.ISpecialProgressService;
import com.wunding.learn.special.service.service.ISpecialService;
import com.wunding.learn.special.service.service.ISpecialStageService;
import com.wunding.learn.special.service.service.ISpecialTagService;
import com.wunding.learn.special.service.service.ISpecialTaskService;
import com.wunding.learn.special.service.service.ISpecialUserStudyConditionService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.SysTagFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 专题主表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2022-08-09
 */
@Slf4j
@Service("specialService")
public class SpecialServiceImpl extends ServiceImpl<SpecialMapper, Special> implements ISpecialService {


    //固定时间
    public static final int FIXED_TIME = 1;
    /**
     * 课程
     */
    private static final String COURSE = "course";

    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private CourseFeign courseFeign;
    @Resource
    private CommentFeign commentFeign;
    @Resource
    private ISpecialStageService specialStageService;
    @Resource
    private ICategorysService categorysService;
    @Resource
    private SpecialViewLimitComponent specialViewLimitComponent;
    @Resource
    private ISpecialTaskService specialTaskService;
    @Resource
    private ISpecialProgressService specialProgressService;
    @Resource
    private SpecialTaskProgressServiceImpl specialTaskProgressService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private ISpecialCashServiceImpl iSpecialCashService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private ISpecialTagService specialTagService;
    @Resource
    private ISpecialUserStudyConditionService specialUserStudyConditionService;
    @Resource(name = "specialDao")
    private SpecialDao specialDao;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private SysTagFeign sysTagFeign;
    @Resource
    private ApplyAuditTemplate<Special> specialApplyAuditTemplate;
    @Resource
    private ProcessFeign processFeign;

    private static void setMonthIdpList(List<IdpSpecialDTO> userIdpSpecialDTOList, UserIdpSpecialDTO userIdpSpecial) {
        if (!CollectionUtils.isEmpty(userIdpSpecialDTOList)) {
            //根据用户专题专题开始时间对专题进行排序
            List<MonthIdpSpecialDTO> monthIdpSpecialList = new ArrayList<>();
            Collections.sort(userIdpSpecialDTOList, ((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())));
            for (int i = 12; i > 0; i--) {
                MonthIdpSpecialDTO monthIdpSpecialDTO = new MonthIdpSpecialDTO();
                monthIdpSpecialDTO.setMonth(i);
                LinkedList<IdpSpecialDTO> userIdpSpecialDTOLinkedList = new LinkedList<>();
                for (IdpSpecialDTO userIdpSpecialDTO : userIdpSpecialDTOList) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(userIdpSpecialDTO.getStartTime());
                    setMonthData(userIdpSpecialDTO, cal, i, userIdpSpecialDTOLinkedList);
                }
                monthIdpSpecialDTO.setIdpList(userIdpSpecialDTOLinkedList);
                monthIdpSpecialList.add(monthIdpSpecialDTO);
            }
            userIdpSpecial.setMonthIdpList(monthIdpSpecialList);
        }
    }

    private static void setMonthData(IdpSpecialDTO userIdpSpecialDTO, Calendar cal, int i,
        LinkedList<IdpSpecialDTO> userIdpSpecialDTOLinkedList) {
        // 注意月份是从0开始的,比如当前7月，获得的month为6
        Integer month = cal.get(Calendar.MONTH) + 1;
        if (month == i) {
            userIdpSpecialDTO.setDesc(null);
            userIdpSpecialDTO.setStatus(0);
            // 用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）
            userIdpSpecialDTO.setUserStatus(0);
            if (userIdpSpecialDTO.getIsFinish() == 1) {
                userIdpSpecialDTO.setStatus(2);
                userIdpSpecialDTO.setUserStatus(2);
            } else {
                if (null != userIdpSpecialDTO.getStartTime() && userIdpSpecialDTO.getStartTime()
                    .before(new Date())) {
                    userIdpSpecialDTO.setStatus(1);
                }
            }
            userIdpSpecialDTOLinkedList.add(userIdpSpecialDTO);
        }
    }


    @Override
    public PageInfo<SpecialListDTO> queryPage(SpecialListQuery specialListQuery) {
        getCurrentJurisdiction(specialListQuery);

        //获取列表
        PageInfo<SpecialListDTO> pageInfo = PageMethod.startPage(specialListQuery.getPageNo(),
            specialListQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.queryPage(specialListQuery));

        Map<String, UserDTO> userNameMapByIds = null;
        List<SpecialListDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> userIds = list.stream().map(SpecialListDTO::getPublishBy).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(userIds)) {
                userNameMapByIds = userFeign.getUserNameMapByIds(userIds);
            }
        }
        Map<String, UserDTO> finalUserNameMapByIds = userNameMapByIds;
        pageInfo.getList().forEach(specialListDTO -> {
            if (Objects.equals(PublishEnum.PUBLISHED.getValue(), specialListDTO.getIsPublish())) {
                UserDTO userDTO = finalUserNameMapByIds.get(specialListDTO.getPublishBy());
                if (userDTO != null) {
                    specialListDTO.setPublishBy(userDTO.getFullName());
                }
            }
            // 替换枚举值成对应字符串
            AuditStatusEnum.transCodeAndSetToStr(specialListDTO);
        });

        if (Objects.equals(specialListQuery.getType(), GeneralJudgeEnum.CONFIRM.getValue())) {
            //路由访问埋点
            mqProducer.sendMsg(
                new HomeRouterVisitEvent(RouterVisitEnum.SubjectManagement.getRouterId(), UserThreadContext.getUserId(),
                    RouterVisitEnum.SubjectManagement.getName()));
        }

        return pageInfo;
    }

    /**
     * 获取当前用户的管辖范围 进行查询
     *
     * @param specialListQuery 专题列表查询对象
     */
    private void getCurrentJurisdiction(SpecialListQuery specialListQuery) {
        String userId = specialListQuery.getCurrentUserId();
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        //管辖范围
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);
        specialListQuery.setCurrentOrgId(orgDTO.getId());
        specialListQuery.setManagerAreaOrgIds(userManageAreaOrgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addSpecial(SaveSpecialDTO saveSpecialDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        checkViewLimit(saveSpecialDTO);
        Special special = new Special();
        saveSpecialDTO.setId(newId());
        BeanUtils.copyProperties(saveSpecialDTO, special);
        // 保存标签关联
        specialTagService.saveOrUpdateSpecialTag(special.getLabel(), special.getId());
        if (StringUtils.isNotBlank(special.getLabel())) {
            Set<String> tagIds = Arrays.stream(special.getLabel().split(",")).collect(Collectors.toSet());
            // 保存标签和专题资源的关系
            sysTagFeign.saveSysTagResourceRelation(tagIds, special.getId(), ResourceTypeEnum.SPECIAL.getCode());
        }
        special.setLabel("");
        // 拼接项目编号（同培训班）
        SimpleDateFormat sdf = new SimpleDateFormat(DateHelper.YYYYMMDD2);
        special.setSpecialNo(sdf.format(new Date()) + StringUtil.random(4));
        // 分类id
        special.setTheMaticClass(saveSpecialDTO.getSpecialClassId());
        // 班主任id
        special.setLeader(saveSpecialDTO.getLeaderId());
        // 创建人部门
        special.setOrgId(orgFeign.getOrgByUserId(UserThreadContext.getUserId()).getId());

        // 保存图片
        saveSpecialImage(saveSpecialDTO, special);

        // 添加默认应用：费用/资料
        StringBuilder projectItem = new StringBuilder("cost,files");
        for (String item : saveSpecialDTO.getProjectItem()) {
            projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(item);
        }
        // 保存应用
        special.setProjectItem(projectItem.toString());

        // 保存阶段
        if (!CollectionUtils.isEmpty(saveSpecialDTO.getSpecialPhaseSave())) {
            saveOrUpdateSpecialStage(saveSpecialDTO.getSpecialPhaseSave(), special.getId());
        }

        // 保存下发范围
        specialViewLimitComponent.handleNewViewLimit(saveSpecialDTO.getProgrammeId(), saveSpecialDTO.getId());

        // 开启了配置才能在添加时直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveSpecialDTO.getIsPublish())) {
            if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.CONFIRM.getValue()
                .equals(Integer.valueOf(paraValue))) {
                special.setIsPublish(saveSpecialDTO.getIsPublish());
                special.setPublishBy(UserThreadContext.getUserId());
                special.setPublishTime(new Date());
            } else {
                throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_HAVE_TASK_EXIST);
            }
        }
        // 分类之前初始化审核信息
        specialApplyAuditTemplate.insertResourceProcessSceneHandle(special);

        // 是否有专题存在审核流程或者审核不通过
        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), special.getIsPublish())
            && !(special.getAuditStatus() == AuditStatusEnum.APPROVED.getCode()
            || special.getAuditStatus() == AuditStatusEnum.NOT_APPLICABLE.getCode())) {
            throw new BusinessException(SpecialErrorNoEnum.EXIST_SPECIAL_NEED_AUDIT);
        }
        specialDao.saveSpecial(special);

        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveSpecialDTO.getIsPublish())) {
            sendPushFeign(saveSpecialDTO, 0);
        }
        // 发送资源同步事件消息
        //培训项目里面的专题不直接出现在任务里面
        if (ObjectUtils.isNotNull(special.getBelongType()) && special.getBelongType() != 1) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.SPECIAL.name(), special.getId(),
                    special.getSpecialName(), special.getStartTime(), special.getEndTime(), 1, special.getIsPublish(),
                    special.getIsDel(), special.getCreateBy(), special.getCreateTime(), special.getUpdateBy(),
                    special.getUpdateTime())));
        }
        return special.getId();
    }

    private void saveSpecialImage(SaveSpecialDTO saveSpecialDTO, Special special) {
        if (StringUtils.isNotBlank(saveSpecialDTO.getCoverImagePath())) {
            fileFeign.deleteImageByBizIdAndBizType(special.getId(), ImageBizType.SPECIAL_COVER.name());
            SaveFileDTO image = fileFeign.saveImage(special.getId(), ImageBizType.SPECIAL_COVER.name(),
                saveSpecialDTO.getCoverImageName(), saveSpecialDTO.getCoverImagePath());
            if (image != null) {
                special.setCoverImage(image.getPath());
                special.setCoverImageName(saveSpecialDTO.getCoverImageName());
            }
        }

        // 保存背景图片
        if (StringUtils.isNotBlank(saveSpecialDTO.getBackImagePath())) {
            fileFeign.deleteImageByBizIdAndBizType(special.getId(), ImageBizType.SPECIAL_BACKGROUND_IMAGE.name());
            SaveFileDTO image = fileFeign.saveImage(special.getId(), ImageBizType.SPECIAL_BACKGROUND_IMAGE.name(),
                saveSpecialDTO.getBackImageName(), saveSpecialDTO.getBackImagePath());
            if (image != null) {
                special.setBackImage(image.getPath());
                special.setBackImageName(saveSpecialDTO.getBackImagePath());
            }
        }
    }

    private void checkViewLimit(SaveSpecialDTO saveSpecialDTO) {
        if (saveSpecialDTO.getViewType() != 1 && saveSpecialDTO.getBelongType() == 0) {
            //对下发范围进行鉴权
            ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
            programmedIdQuery.setNewProgrammeId(saveSpecialDTO.getProgrammeId());
            boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
            if (!checkViewLimit) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
            }
        }
    }

    /**
     * 调用推送feign
     *
     * @param operateState 操作状态
     */
    private void sendPushFeign(SaveSpecialDTO dto, Integer operateState) {

        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        //来自培训项目的专题，没有推送的信息。
        if (dto.getPushNoticeSetDTO() == null) {
            return;
        }
        PushNoticeSetDTO pushNoticeSetDTO = dto.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(dto.getId(), ImageBizType.SPECIAL_COVER.name()));
                }
            });
        }

        // 为空默认是0
        String resourceName = dto.getSpecialName();
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
            .setResourceName(resourceName).setResourceType(PushType.SPECIAL_TOPIC.getKey())
            .setIsTrain(IsTrainEnum.ITSELF.getValue())
            .setOperateState(operateState).setProgrammeId(dto.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();
        pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        pushAttributeDTO.setExistSecondary(false);
        pushAttributeDTO.setStartTime(dto.getStartTime());
        pushAttributeDTO.setEndTime(dto.getEndTime());
        pushAttributeDTO.setIntro(dto.getSpecialDesc());

        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    @Override
    public void deleteSpecial(String ids) {
        List<String> idList = Arrays.asList(ids.split(CommonConstants.A_COMMA_IN_ENGLISH));

        // 已发布资源不能直接删除
        List<Special> specialList = listByIds(idList);
        if (!CollectionUtils.isEmpty(
            specialList.stream().filter(s -> GeneralJudgeEnum.CONFIRM.getValue().equals(s.getIsPublish()))
                .collect(Collectors.toSet()))) {
            throw new BusinessException(BaseErrorNoEnum.INCLUDING_IS_PUBLISH);
        }

        specialList.forEach(e -> {
            specialDao.delSpecial(e);
            // 删除标签关联关系
            sysTagFeign.saveSysTagResourceRelation(new HashSet<String>(), e.getId(), "");
        });

        // 删除专题考试任务
        deleteSpecialExam(ids);

        // 发送资源修改信息
        mqProducer.sendMsg(
            new ResourceChangeEvent(FirstInfoContentEnum.special.name(), idList, GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));

        // 发送资源操作事件消息
        idList.forEach(id -> mqProducer.sendMsg(
            new ResourceOperateEvent(OperationEnum.DELETE, PushType.SPECIAL_TOPIC.getKey(), id)));

        idList.forEach(specialViewLimitComponent::delViewLimit);
        // 删除审核流程实例
        processFeign.deleteProcessInstanceAndTaskByResourceId(idList);
        specialList.forEach(special ->
            // 发送资源同步事件消息
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.SPECIAL.name(), special.getId(), null, null,
                    null, null, null, 1, null, null, UserThreadContext.getUserId(), new Date())))
        );
    }

    // 删除专题考试任务
    private void deleteSpecialExam(String ids) {
        List<String> idList = Arrays.asList(ids.split(CommonConstants.A_COMMA_IN_ENGLISH));
        LambdaQueryWrapper<SpecialTask> queryWrapper = new LambdaQueryWrapper<SpecialTask>()
            .select(SpecialTask::getTaskContent)
            .in(SpecialTask::getSpecialId, idList)
            .eq(SpecialTask::getCreateType, ProjectConstant.CREATE_TYPE_DIRECT)
            .eq(SpecialTask::getTaskType, SpecialTaskTypeEnum.EXAM.getTaskType());
        List<SpecialTask> examTaskList = specialTaskService.list(queryWrapper);
        if (examTaskList.isEmpty()) {
            return;
        }

        // 获取考试ID列表
        idList = examTaskList.stream().map(SpecialTask::getTaskContent).collect(Collectors.toList());

        // 组装考试ID
        ids = String.join(",", idList);

        // 不再校验考试的发布状态，直接执行删除操作
        examFeign.removeExam(ids, GeneralJudgeEnum.NEGATIVE.getValue());

    }

    @Override
    public void publish(PublishDTO publishDTO) {
        // 是否有专题存在审核流程或者审核不通过
        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), publishDTO.getIsPublish())
            && baseMapper.isExistSpecialNeedAudit(publishDTO.getIds())) {
            throw new BusinessException(SpecialErrorNoEnum.EXIST_SPECIAL_NEED_AUDIT);
        }
        List<Special> list = listByIds(publishDTO.getIds());
        if (publishDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
            // 开启了配置才能在添加时直接发布
            String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
            if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue()
                .equals(Integer.valueOf(paraValue))) {
                // 先校验专题下面是否有任务（没有任务的不允许发布）
                long count = specialTaskService.count(
                    new LambdaQueryWrapper<SpecialTask>().in(SpecialTask::getSpecialId, publishDTO.getIds()));
                if (count == 0) {
                    throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_HAVE_TASK_EXIST);
                }
            }

            list.forEach(e -> specialDao.publish(e));
        } else {
            list.forEach(e -> specialDao.unPublish(e));
        }

        // 发送资源修改信息
        mqProducer.sendMsg(new ResourceChangeEvent(FirstInfoContentEnum.special.name(), publishDTO.getIds(),
            GeneralJudgeEnum.NEGATIVE.getValue(), publishDTO.getIsPublish()));

        // 发送资源操作事件消息
        publishDTO.getIds().forEach(id -> mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(publishDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.SPECIAL_TOPIC.getKey(), id)));

        publishDTO.getIds().forEach(id -> {
            // 发送资源同步事件消息
            Special special = getById(id);
            if (ObjectUtils.isNotNull(special.getBelongType()) && special.getBelongType() != 1) {
                mqProducer.sendMsg(new ResourceSyncEvent(
                    new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.SPECIAL.name(), id, null, null, null,
                        null,
                        publishDTO.getIsPublish(), null, null, null, UserThreadContext.getUserId(), new Date())));
            }
        });
    }

    @Override
    public SpecialDTO getSpecial(String id) {
        Special special = getById(id);
        if (special == null) {
            throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_EXIST);
        }
        SpecialDTO specialDTO = new SpecialDTO();
        BeanUtils.copyProperties(special, specialDTO);
        // 返回归属部门名称
        specialDTO.setOrgName(orgFeign.getById(special.getOrgId()).getOrgName());
        // 专题封面图
        List<NamePath> pathList = fileFeign.getImageFileNamePaths(id, ImageBizType.SPECIAL_COVER.name());
        if (!pathList.isEmpty()) {
            specialDTO.setCoverImageUrl(pathList.get(0).getUrl());
            specialDTO.setCoverImageName(pathList.get(0).getName());
            specialDTO.setCoverImagePath(pathList.get(0).getPath());
        }
        // 专题背景图
        List<NamePath> backgroundPathList = fileFeign.getImageFileNamePaths(id,
            ImageBizType.SPECIAL_BACKGROUND_IMAGE.name());
        if (!backgroundPathList.isEmpty()) {
            specialDTO.setBackImageUrl(backgroundPathList.get(0).getUrl());
            specialDTO.setBackImageName(backgroundPathList.get(0).getName());
            specialDTO.setBackImagePath(backgroundPathList.get(0).getPath());
        }
        // 班主任
        if (StringUtils.isNotEmpty(special.getLeader())) {
            String fullName = userFeign.getUserFullNameById(special.getLeader());
            if (fullName != null) {
                specialDTO.setLeaderId(special.getLeader());
                specialDTO.setLeaderName(fullName);
            }
        }
        // 应用 去除默认的两个应用，不然前端会带入编辑保存中，就会导致应用重复
        specialDTO.setProjectItem(
            Arrays.asList(special.getProjectItem().split(CommonConstants.A_COMMA_IN_ENGLISH).clone()).stream()
                .filter(item -> Objects.equals(item, SpecialAppType.COMPLETION.getAppType()) || Objects.equals(item,
                    SpecialAppType.NOTICE.getAppType())).collect(Collectors.toList()));

        //下发范围
        specialDTO.setLimit(specialViewLimitComponent.getViewLimitBaseInfo(id));

        // 项目阶段
        specialDTO.setSpecialPhaseSave(phaseList(id));

        // 专题标签
        List<String> tags = specialTagService.list(
                new LambdaQueryWrapper<SpecialTag>().eq(SpecialTag::getSpecialId, id)).stream().map(SpecialTag::getTagId)
            .collect(Collectors.toList());
        if (!tags.isEmpty()) {
            specialDTO.setLabel(String.join(",", tags));
        }

        // 专题分类
        if (StringUtils.isNotEmpty(special.getTheMaticClass())) {
            specialDTO.setSpecialClassId(special.getTheMaticClass());
            specialDTO.setSpecialClassName(categorysService.getById(special.getTheMaticClass()).getCategoryName());
            specialDTO.setCateLevelPath(categorysService.getById(special.getTheMaticClass()).getLevelPath());
        }
        // 替换枚举值成对应字符串
        AuditStatusEnum.transCodeAndSetToStr(specialDTO);
        return specialDTO;
    }

    @Override
    public List<ProphaseDTO> phaseList(String specialId) {
        // 专题阶段
        List<SpecialStage> phaseList = specialStageService.list(
            new LambdaQueryWrapper<SpecialStage>().eq(SpecialStage::getSpecialId, specialId)
                .orderByAsc(SpecialStage::getSortNo));
        List<ProphaseDTO> prophaseDTOList = new ArrayList<>();
        for (SpecialStage stage : phaseList) {
            ProphaseDTO prophaseDTO = new ProphaseDTO();
            prophaseDTO.setId(stage.getId());
            prophaseDTO.setName(stage.getStageName());
            prophaseDTO.setSort(stage.getSortNo());
            List<NamePath> phaseImgList = fileFeign
                .getImageFileNamePaths(prophaseDTO.getId(), ImageBizType.SPECIAL_PHASE_IMAGE.name());
            prophaseDTO.setPhaseImgList(phaseImgList);
            prophaseDTOList.add(prophaseDTO);
        }
        return prophaseDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpecial(UpdateSpecialDTO updateSpecialDTO) {
        //判断专题是否满足发布条件
        checkSpecialPublish(updateSpecialDTO);
        checkSpecialVisitArea(updateSpecialDTO);
        Special special = new Special();
        Special oldSpecial = getById(updateSpecialDTO.getId());
        BeanUtils.copyProperties(updateSpecialDTO, special);
        // 判断专题图片是否修改
        updateCoverImage(updateSpecialDTO, oldSpecial, special);
        // 判断专题背景图片是否修改
        if (StringUtils.isNotBlank(updateSpecialDTO.getBackImagePath()) && !Objects.equals(oldSpecial.getBackImage(),
            updateSpecialDTO.getBackImagePath())) {
            fileFeign.deleteImageByBizIdAndBizType(updateSpecialDTO.getId(),
                ImageBizType.SPECIAL_BACKGROUND_IMAGE.name());
            SaveFileDTO image = fileFeign.saveImage(updateSpecialDTO.getId(),
                ImageBizType.SPECIAL_BACKGROUND_IMAGE.name(), updateSpecialDTO.getBackImageName(),
                updateSpecialDTO.getBackImagePath());
            if (image != null) {
                special.setBackImage(image.getPath());
                special.setBackImageName(updateSpecialDTO.getBackImageName());
            }
        }
        special.setTheMaticClass(updateSpecialDTO.getSpecialClassId());
        // 保存阶段
        if (!CollectionUtils.isEmpty(updateSpecialDTO.getSpecialPhaseSave())) {
            saveOrUpdateSpecialStage(updateSpecialDTO.getSpecialPhaseSave(), special.getId());
        }

        // 添加默认应用：费用/资料
        if (!CollectionUtils.isEmpty(updateSpecialDTO.getProjectItem())) {
            StringBuilder projectItem = new StringBuilder("cost,files");
            for (String item : updateSpecialDTO.getProjectItem()) {
                projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(item);
            }
            special.setProjectItem(projectItem.toString());
        }

        // 保存下发范围
        specialViewLimitComponent.handleNewViewLimit(updateSpecialDTO.getProgrammeId(), updateSpecialDTO.getId());

        //更新头条下发范围
        mqProducer.sendMsg(
            new FirstInfoViewLimitChangeEvent(updateSpecialDTO.getId(), FirstInfoContentEnum.special.name(),
                updateSpecialDTO.getProgrammeId()));
        special.setPublishBy(UserThreadContext.getUserId());

        // 记录最初的审核状态防止空指针
        special.setAuditStatus(oldSpecial.getAuditStatus());
        // 判断专题分类是否修改,修改了需要未发布处理
        specialApplyAuditTemplate.updateResourceProcessSceneHandle(special, oldSpecial);
        // 是否有专题存在审核流程或者审核不通过
        if (Objects.equals(PublishStatusEnum.IS_PUBLISH.getValue(), special.getIsPublish())
            && !(special.getAuditStatus() == AuditStatusEnum.APPROVED.getCode()
            || special.getAuditStatus() == AuditStatusEnum.NOT_APPLICABLE.getCode())) {
            throw new BusinessException(SpecialErrorNoEnum.EXIST_SPECIAL_NEED_AUDIT);
        }
        specialDao.updateSpecial(special);
        // 保存标签关联
        specialTagService.saveOrUpdateSpecialTag(updateSpecialDTO.getLabel(), special.getId());
        if (StringUtils.isNotBlank(updateSpecialDTO.getLabel())) {
            Set<String> tagIds = Arrays.stream(updateSpecialDTO.getLabel().split(",")).collect(Collectors.toSet());
            // 保存标签和专题资源的关系
            sysTagFeign.saveSysTagResourceRelation(tagIds, special.getId(), ResourceTypeEnum.SPECIAL.getCode());
        }
        // 同步修改直接创建的任务资源下发
        List<SpecialTask> specialTaskList = specialTaskService.list(
            new QueryWrapper<SpecialTask>().lambda().eq(SpecialTask::getIsDel, 0)
                .eq(SpecialTask::getCreateType, ProjectConstant.CREATE_TYPE_DIRECT)
                .eq(SpecialTask::getSpecialId, updateSpecialDTO.getId()));
        specialTaskList.forEach(task -> specialTaskService.syncSaveViewLimit(task));

        // 编辑通知设置
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(updateSpecialDTO.getIsPublish())) {
            sendPushFeign(updateSpecialDTO, 1);
        }
        // 发送资源同步事件消息

        if (ObjectUtils.isNotNull(special.getBelongType()) && special.getBelongType() != 1) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.SPECIAL.name(), special.getId(),
                    special.getSpecialName(), special.getStartTime(), special.getEndTime(), 1, special.getIsPublish(),
                    special.getIsDel(), special.getCreateBy(), special.getCreateTime(), special.getUpdateBy(),
                    special.getUpdateTime())));
        }
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(updateSpecialDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.SPECIAL_TOPIC.getKey(),
            special.getId()));
        mqProducer.sendMsg(
            new ResourceOperateEvent(OperationEnum.UPDATE, PushType.SPECIAL_TOPIC.getKey(), special.getId()));
    }

    private void updateCoverImage(UpdateSpecialDTO updateSpecialDTO, Special oldSpecial, Special special) {
        if (StringUtils.isNotBlank(updateSpecialDTO.getCoverImageName()) && !Objects.equals(oldSpecial.getCoverImage(),
            updateSpecialDTO.getCoverImagePath())) {
            fileFeign.deleteImageByBizIdAndBizType(updateSpecialDTO.getId(), ImageBizType.SPECIAL_COVER.name());
            SaveFileDTO image = fileFeign.saveImage(updateSpecialDTO.getId(), ImageBizType.SPECIAL_COVER.name(),
                updateSpecialDTO.getCoverImageName(), updateSpecialDTO.getCoverImagePath());
            if (image != null) {
                special.setCoverImage(image.getPath());
                special.setCoverImageName(updateSpecialDTO.getCoverImageName());
            }
        }
    }

    private void checkSpecialPublish(UpdateSpecialDTO updateSpecialDTO) {
        // 开启了配置才能在添加时直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue().equals(Integer.valueOf(paraValue))
            && updateSpecialDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
            // 先校验专题下面是否有任务（没有任务的不允许发布）
            long count = specialTaskService.count(
                new LambdaQueryWrapper<SpecialTask>().in(SpecialTask::getSpecialId, updateSpecialDTO.getId()));
            if (count == 0) {
                throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_HAVE_TASK_EXIST);
            }
        }
    }

    private void checkSpecialVisitArea(UpdateSpecialDTO updateSpecialDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        if (updateSpecialDTO.getViewType() != 1) {
            //查询专题的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(updateSpecialDTO.getId());
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!Objects.equals(programmeId, updateSpecialDTO.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setOldProgrammeId(programmeId);
                programmedIdQuery.setNewProgrammeId(updateSpecialDTO.getProgrammeId());
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
    }

    /**
     * 调用推送feign
     *
     * @param operateState 操作状态
     */
    private void sendPushFeign(UpdateSpecialDTO dto, Integer operateState) {

        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = dto.getPushNoticeSetDTO();
        //来自培训项目的专题，没有推送的信息。
        if (dto.getPushNoticeSetDTO() == null) {
            return;
        }
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(dto.getId(), ImageBizType.SPECIAL_COVER.name()));
                }
            });
        }
        // 推送资源dto对象
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
            .setResourceName(dto.getSpecialName()).setResourceType(PushType.SPECIAL_TOPIC.getKey())
            .setIsTrain(IsTrainEnum.ITSELF.getValue()).setOperateState(operateState)
            .setProgrammeId(dto.getProgrammeId());
        sendPushDTO.setPushResourceDTO(pushResourceDTO);
        // 推送标签属性dto对象
        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();
        pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        pushAttributeDTO.setExistSecondary(false);
        pushAttributeDTO.setStartTime(dto.getStartTime());
        pushAttributeDTO.setEndTime(dto.getEndTime());
        pushAttributeDTO.setIntro(dto.getSpecialDesc());
        // 平台名称
        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);
        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    /**
     * 添加专题时，添加专题阶段
     *
     * @param id                      专题id
     * @param specialPhaseSaveDTOList 专题阶段列表
     */
    public void saveOrUpdateSpecialStage(List<SpecialPhaseSaveDTO> specialPhaseSaveDTOList, String id) {
        ArrayList<SpecialStage> list = new ArrayList<>();
        List<ChooseListDTO> dbList = specialStageService.getSpecialPhaseList(id);
        Special byId = getById(id);
        if (byId == null) {
            saveSpecialStage(specialPhaseSaveDTOList, id, list);
        } else {
            updateSpecialStage(specialPhaseSaveDTOList, id, dbList, list);
        }
    }

    private void updateSpecialStage(List<SpecialPhaseSaveDTO> specialPhaseSaveDTOList, String id,
        List<ChooseListDTO> dbList,
        ArrayList<SpecialStage> list) {
        // 编辑
        if (!CollectionUtils.isEmpty(dbList)) {
            Map<String, SpecialPhaseSaveDTO> map = new HashMap<>();
            for (SpecialPhaseSaveDTO item : specialPhaseSaveDTOList) {
                map.put(item.getId(), item);
            }
            List<String> delListIds = dbList.stream().filter(dto -> !map.containsKey(dto.getId()))
                .map(ChooseListDTO::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delListIds)) {
                specialStageService.removeByIds(delListIds);
            }
            for (SpecialPhaseSaveDTO specialPhaseSaveDTO : specialPhaseSaveDTOList) {
                SpecialStage specialStage = new SpecialStage();
                if (StringUtils.isEmpty(specialPhaseSaveDTO.getId())) {
                    specialStage.setId(newId());
                } else {
                    specialStage.setId(specialPhaseSaveDTO.getId());
                }
                specialStage.setStageName(specialPhaseSaveDTO.getName());
                specialStage.setSortNo(specialPhaseSaveDTO.getSort());
                specialStage.setSpecialId(id);
                specialStage.setRemark(specialPhaseSaveDTO.getRemark());
                list.add(specialStage);
                //保存专题阶段图片
                NamePath phaseImg = specialPhaseSaveDTO.getPhaseImg();
                if (null != phaseImg && !StringUtils.isEmpty(phaseImg.getPath())) {
                    fileFeign.deleteImageByBizIdAndBizType(specialStage.getId(),
                        ImageBizType.SPECIAL_PHASE_IMAGE.name());
                    fileFeign.saveImage(specialStage.getId(), ImageBizType.SPECIAL_PHASE_IMAGE.name(),
                        phaseImg.getName(), phaseImg.getPath());
                }

            }
            specialStageService.saveOrUpdateBatch(list);
        }
    }

    private void saveSpecialStage(List<SpecialPhaseSaveDTO> specialPhaseSaveDTOList, String id,
        ArrayList<SpecialStage> list) {
        // 添加
        for (SpecialPhaseSaveDTO specialPhaseSaveDTO : specialPhaseSaveDTOList) {
            SpecialStage specialStage = new SpecialStage();
            if (StringUtils.isEmpty(specialPhaseSaveDTO.getId())) {
                specialStage.setId(newId());
            } else {
                specialStage.setId(specialPhaseSaveDTO.getId());
            }
            specialStage.setStageName(specialPhaseSaveDTO.getName());
            specialStage.setSortNo(specialPhaseSaveDTO.getSort());
            specialStage.setSpecialId(id);
            specialStage.setRemark(specialPhaseSaveDTO.getRemark());
            list.add(specialStage);
            //保存专题阶段图片
            NamePath phaseImg = specialPhaseSaveDTO.getPhaseImg();
            if (null != phaseImg && !StringUtils.isEmpty(phaseImg.getPath())) {
                fileFeign
                    .saveImage(specialStage.getId(), ImageBizType.SPECIAL_PHASE_IMAGE.name(), phaseImg.getName(),
                        phaseImg.getPath());
            }

        }
        specialStageService.saveOrUpdateBatch(list);
    }

    @Override
    public List<ChooseListDTO> getSpecialLabelList() {
        List<Categorys> list = categorysService.list(
            new QueryWrapper<Categorys>().lambda().eq(Categorys::getIsAvailable, 1)
                .eq(Categorys::getCategoryType, CategoryType.ThematicTag).orderByAsc(Categorys::getSortNo));
        List<ChooseListDTO> listDTOList = new ArrayList<>();
        list.forEach(category -> {
            ChooseListDTO dto = new ChooseListDTO();
            dto.setId(category.getId());
            dto.setName(category.getCategoryName());
            listDTOList.add(dto);
        });
        return listDTOList;
    }

    @Override
    public void completionProject(String specialId) {
        Special byId = getById(specialId);
        specialDao.completionProject(byId);
    }

    @Override
    public PageInfo<SpecialApiListDTO> getSpecialApiList(SpecialApiListQuery specialApiListQuery) {
        // 时间默认查询为倒序
        if (StringUtils.isEmpty(specialApiListQuery.getOrder())) {
            specialApiListQuery.setOrder("timedown");
        }
        // 处理查询条件
        convertCriteria(specialApiListQuery);
        //获取列表
        PageInfo<SpecialApiListDTO> pageInfo = PageMethod.startPage(specialApiListQuery.getPageNo(),
                specialApiListQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.getSpecialList(specialApiListQuery));
        pageInfo.setIsLastPage(specialApiListQuery.getPageSize() != pageInfo.getList().size());
        List<SpecialApiListDTO> pageInfoList = pageInfo.getList();
        if (pageInfoList.isEmpty()) {
            return pageInfo;
        }
        Set<String> specialIds = pageInfoList.stream().map(SpecialApiListDTO::getId).collect(Collectors.toSet());
        Map<String, String> imageMap = fileFeign.getImageUrlsByIds(specialIds, ImageBizType.SPECIAL_COVER.name());

        pageInfoList.forEach(special -> {
            String specialId = special.getId();
            special.setImageUrl(imageMap.get(specialId));
            special.setIsAttend(false);
            SpecialApiListDTO dto = iSpecialCashService.getSpecialCashById(specialId);
            CopyNotNullObjectUtil.copyProperties(dto, special);
            if (special.getType().equals(SpecialConstant.PROJECT_TYPE_FIXED_DATE)) {
                Date startTime = special.getStartTime();
                Date endTime = special.getEndTime();
                if (startTime.compareTo(new Date()) > 0) {
                    special.setStatus(1);
                }
                if (endTime.compareTo(new Date()) < 0) {
                    special.setStatus(2);
                }
            }
        });
        return pageInfo;
    }

    /**
     * 获取当前用户 处理查询条件
     *
     * @param specialApiListQuery 学员端专题查询列表对象
     */
    private void convertCriteria(SpecialApiListQuery specialApiListQuery) {
        specialApiListQuery.setCurrentUserId(UserThreadContext.getUserId());
        // 处理查询条件
        specialApiListQuery.setStatusList(
            TranslateUtil.translateBySplit(specialApiListQuery.getStatus(), Integer.class));
        specialApiListQuery.setFilterCateIdList(
            TranslateUtil.translateBySplit(specialApiListQuery.getFilterCateId(), String.class));
        specialApiListQuery.setFilterTagIdList(
            TranslateUtil.translateBySplit(specialApiListQuery.getFilterTagId(), String.class));
    }

    @Override
    public SpecialFilterListDTO getFilterCriteria() {
        SpecialFilterListDTO filterListDTO = new SpecialFilterListDTO();
        // 查询类别列表
        filterListDTO.setThematicClassList(queryCategoryByType(CategoryType.ThematicClass.name()));
        // 查询标签列表
        filterListDTO.setThematicTagList(queryCategoryByType(CategoryType.ThematicTag.name()));
        return filterListDTO;
    }

    @Override
    public SpecialCategoryFilterListDTO getSpecialClassify() {
        SpecialCategoryFilterListDTO specialCategoryFilterListDTO = new SpecialCategoryFilterListDTO();
        // 查询类别列表
        specialCategoryFilterListDTO.setThematicClassList(getCategoryDTOListByType(CategoryType.ThematicClass.name()));
        // 查询标签列表
        specialCategoryFilterListDTO.setThematicTagList(getCategoryDTOListByType(CategoryType.ThematicTag.name()));
        return specialCategoryFilterListDTO;
    }

    private List<SpecialCategoryClientDTO> getCategoryDTOListByType(String categoryType) {

        // 获取分类列表
        List<Categorys> courseCategoryList = categorysService.getViewLimitCategoryList(categoryType,
            null, LimitTable.SpecialViewLimit.name());

        if (CollectionUtils.isEmpty(courseCategoryList)) {
            return Collections.emptyList();
        }

        // 转成树结构返回
        return toTreeResult(courseCategoryList, categoryType);
    }

    private List<SpecialCategoryClientDTO> toTreeResult(List<Categorys> courseCategoryList, String categoryType) {
        Map<String, List<Categorys>> parentIdListMap = courseCategoryList.stream()
            .collect(Collectors.groupingBy(Categorys::getParentId));
        return getCategorySubList(parentIdListMap, "", categoryType);
    }

    private List<SpecialCategoryClientDTO> getCategorySubList(Map<String, List<Categorys>> parentIdListMap,
        String parentId, String categoryType) {
        List<Categorys> categoryList = parentIdListMap.get(parentId);
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        categoryList.sort((c1, c2) -> {
            if (c1 == null || c2 == null) {
                return 0;
            }
            int c = c1.getSortNo() - c2.getSortNo();
            if (c == 0) {
                return StringUtils.compare(c1.getId(), c2.getId());
            }
            return c;
        });
        List<SpecialCategoryClientDTO> result = new ArrayList<>();
        for (Categorys category : categoryList) {
            SpecialCategoryClientDTO dto = new SpecialCategoryClientDTO();
            dto.setId(category.getId());
            dto.setCategoryName(category.getCategoryName());
            dto.setType(categoryType);
            dto.setChildren(getCategorySubList(parentIdListMap, category.getId(), categoryType));
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据类型查询
     *
     * @param categoryType 分类类型
     * @return 专题筛选列表对象
     */
    private List<IdName> queryCategoryByType(String categoryType) {
        List<IdName> filterList = new ArrayList<>();
        categorysService.list(new QueryWrapper<Categorys>().lambda().eq(Categorys::getIsAvailable, 1)
                .eq(Categorys::getCategoryType, categoryType).eq(Categorys::getIsDel, 0).orderByAsc(Categorys::getSortNo))
            .forEach(category -> {
                IdName filter = new IdName();
                filter.setId(category.getId());
                filter.setName(category.getCategoryName());
                filterList.add(filter);
            });
        return filterList;
    }

    @Override
    public Special specialExist(String id) {
        Special special = this.getById(id);
        if (Objects.isNull(special)) {
            throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_EXIST);
        }
        return special;
    }

    @Override
    public List<GroupSpecialTaskDTO> getDetailTaskList(String id, Integer isIgnoreView) {
        // 加入专题
        joinSpecial(id, isIgnoreView);
        // 分页获取专题任务
        List<SpecialTaskDTO> taskList = baseMapper.getTaskList(id, UserThreadContext.getUserId());
        // 过滤出引用创建的任务
        List<SpecialTaskDTO> quoteTask = taskList.stream().filter(task -> task.getCreateType() == 0)
            .collect(Collectors.toList());
        log.info("quoteTask： " + quoteTask);
        ArrayList<SpecialTaskDTO> filterTask = new ArrayList<>();
        // 这里循环中遍历不行，可以改成根据任务类型去批量查询获取Map映射
        for (SpecialTaskDTO specialTaskDTO : quoteTask) {
            // 获取资源发布状态
            int isDeleteOrNotPublish = Objects.requireNonNull(
                    SpecialTaskTypeEnum.getStrategy(specialTaskDTO.getTaskType()))
                .getResourceIsNotDeleteAndIsPublish(specialTaskDTO.getContentId());
            // 过滤出资源被删除或未发布的任务
            if (isDeleteOrNotPublish == 1) {
                filterTask.add(specialTaskDTO);
            }
        }
        log.info("filterTask: " + filterTask);
        if (!filterTask.isEmpty()) {
            taskList.removeAll(filterTask);
        }
        log.info("taskList: " + taskList);
        if (taskList.isEmpty()) {
            return Collections.emptyList();
        }
        log.info("filter_special_task_list:[{}],special_id:[{}]", taskList, id);
        Set<String> taskIds = taskList.stream().map(SpecialTaskDTO::getId).collect(Collectors.toSet());
        Map<String, NamePath> pathMap = fileFeign.getImageFileNamePathsByBizIds(taskIds,
                ImageBizType.SPECIAL_TASK_IMAGE.name()).stream()
            .collect(Collectors.toMap(NamePath::getCategoryId, namePath -> namePath, (key1, key2) -> key1));
        Set<String> courseIds = taskList.stream().filter(specialTaskDTO -> COURSE.equals(specialTaskDTO.getTaskType()))
            .map(SpecialTaskDTO::getContentId).collect(Collectors.toSet());
        Map<String, CourseInfoDTO> courseInfoDTOMap = Collections.emptyMap();
        if (!courseIds.isEmpty()) {
            // 可以异步处理再整合
            courseInfoDTOMap = courseFeign.getCommentCount(courseIds);
        }
        final Map<String, CourseInfoDTO> courseInfoMap = courseInfoDTOMap;

        // 根据任务id获取任务
        Map<String, SpecialTaskDTO> taskDTOMap = taskList.stream()
            .collect(Collectors.toMap(SpecialTaskDTO::getId, Function.identity(), (k1, k2) -> k1));
        taskList.forEach(specialTaskDTO -> {
            // 拼装专题信息
            SpecialTaskDTO taskDTO = specialTaskService.getSpecialTaskCashById(specialTaskDTO.getId());
            CopyNotNullObjectUtil.copyProperties(taskDTO, specialTaskDTO);
            if (StringUtils.isNotBlank(specialTaskDTO.getPreTaskId())) {
                SpecialTaskDTO specialTaskCashById = specialTaskService.getSpecialTaskCashById(
                    specialTaskDTO.getPreTaskId());
                if (specialTaskCashById != null) {
                    specialTaskDTO.setPreTaskName(specialTaskCashById.getTitle());
                }
            }

            // 设置前置任务状态
            setPreStatus(specialTaskDTO, taskDTOMap);

            NamePath namePath = pathMap.get(specialTaskDTO.getId());
            specialTaskDTO.setTaskIconImage(namePath.getUrl());

            // 设置评论量 浏览量
            extracted(specialTaskDTO, courseInfoMap);
        });

        // LinkedHashMap 保证放入元素的顺序
        Map<String, List<SpecialTaskDTO>> listMap = taskList.stream()
            .collect(Collectors.groupingBy(SpecialTaskDTO::getStageName, LinkedHashMap::new, Collectors.toList()));
        List<GroupSpecialTaskDTO> resultGroup = new ArrayList<>();
        listMap.forEach((key, value) -> {
            GroupSpecialTaskDTO groupSpecialTaskDTO = new GroupSpecialTaskDTO();
            groupSpecialTaskDTO.setChapterTitle(key).setTaskList(value);
            resultGroup.add(groupSpecialTaskDTO);
        });
        return resultGroup;
    }

    // 设置评论量 浏览量
    private static void extracted(SpecialTaskDTO specialTaskDTO, Map<String, CourseInfoDTO> courseInfoMap) {
        if (COURSE.equals(specialTaskDTO.getTaskType())) {
            String courseId = specialTaskDTO.getContentId();
            CourseInfoDTO courseInfoDTO = courseInfoMap.get(courseId);
            if (courseInfoDTO != null) {
                specialTaskDTO.setVc(Math.toIntExact(courseInfoDTO.getClickNumber()))
                    .setLikeCount(String.valueOf(Optional.ofNullable(courseInfoDTO.getVoteNumber()).orElse(0)))
                    .setDiscussCount(
                        String.valueOf(Optional.ofNullable(courseInfoDTO.getCommentNumber()).orElse(0)));
            }
        } else {
            specialTaskDTO.setVc(-1).setLikeCount("-1").setDiscussCount("-1");
        }
    }

    // 设置前置任务状态
    private static void setPreStatus(SpecialTaskDTO specialTaskDTO, Map<String, SpecialTaskDTO> taskDTOMap) {
        // 当有前置任务时
        if (specialTaskDTO.getPreStatus() == 1) {
            String preTaskId = specialTaskDTO.getPreTaskId();
            SpecialTaskDTO preTaskDTO = taskDTOMap.get(preTaskId);
            if (null != preTaskDTO && preTaskDTO.getStatus() == 2) {
                specialTaskDTO.setPreStatus(0);
            }
        }
    }

    private void joinSpecial(String specialId, Integer isIgnoreView) {
        String userId = UserThreadContext.getUserId();
        // 校验专题是否存在
        Special special = specialExist(specialId);
        // 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView) && Boolean.FALSE.equals(
            specialViewLimitComponent.checkViewLimit(userId, special.getId()))) {
            throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NO_PERMISSION);
        }
        // 判断是否第一次加入专题
        boolean isJoin = specialProgressService.isJoinSpecial(special.getId(), userId);
        if (isJoin) {
            return;
        }
        //第一次加入则保存
        //保存专题学习信息用户学习情况表记录
        specialUserStudyConditionService.insertSpecialUserStudyCondition(userId, specialId);
        // 加入专题
        String orgId = UserThreadContext.getOrgId();
        String orgLevelPath = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(orgId)) {
            OrgDTO orgDTO = orgFeign.getById(orgId);
            orgLevelPath = Objects.nonNull(orgDTO) ? orgDTO.getLevelPath() : "";
        }
        // 对该方法开启事务,保证一致性
        specialTaskService.handleInitSpecialProgress(special, userId, orgId, orgLevelPath);
    }


    /**
     * 当前时间是否在专题时间内
     *
     * @param special 项目
     */
    @Override
    public void specialTimeCheck(Special special) {
        Integer type = special.getType();
        if (type.equals(SpecialConstant.PROJECT_TYPE_FIXED_DATE)) {
            Date startTime = special.getStartTime();
            Date endTime = special.getEndTime();
            if (startTime.compareTo(new Date()) > 0) {
                throw new BusinessException(SpecialErrorNoEnum.SPECIAL_NOT_START);
            }
            if (endTime.compareTo(new Date()) < 0) {
                throw new BusinessException(SpecialErrorNoEnum.SPECIAL_END);
            }
        }
    }

    @Override
    @RedisCacheable(cacheName = "getDetail")
    public SpecialDetailDTO getDetail(String id, Integer isIgnoreView) {
        Special special = getById(id);
        SpecialDetailDTO specialDetailDTO = new SpecialDetailDTO();
        // 1 校验权限
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)
            && !resourceViewLimitService.checkViewLimit(id, LimitTable.SpecialViewLimit.name(),
            UserThreadContext.getUserId())) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
        // 2 查该学习项目是否发布
        Special specialInfo = baseMapper.selectOne(new LambdaQueryWrapper<Special>().eq(Special::getId, id)
            .eq(Special::getIsPublish, PublishEnum.PUBLISHED.getValue()));
        if (null == specialInfo) {
            throw new BusinessException(SpecialErrorNoEnum.ERROR_SPECIAL_IS_NO_PUBLISH);
        }
        // 校验发布
        if (special != null) {
            specialTimeCheck(special);
            specialDetailDTO.setDescription(special.getSpecialDesc());
            specialDetailDTO.setBackType(special.getBackType());
            specialDetailDTO.setTitle(special.getSpecialName());
            specialDetailDTO.setHeadImg(fileFeign.getImageUrl(special.getId(), ImageBizType.SPECIAL_COVER.name()));
            specialDetailDTO.setBackImage(
                fileFeign.getImageUrl(special.getId(), ImageBizType.SPECIAL_BACKGROUND_IMAGE.name()));
        } else {
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        long taskCount = specialTaskService.count(
            Wrappers.<SpecialTask>lambdaQuery().eq(SpecialTask::getSpecialId, id));
        return specialDetailDTO.setTaskCount((int) taskCount);
    }

    @Override
    public void checkProjectTaskProgressIsAllFinish(String specialId, String userId) {
        // 获取专题内的任务列表
        List<String> taskIdList = specialTaskService.lambdaQuery()
            .eq(SpecialTask::getIsPublish, GeneralJudgeEnum.CONFIRM.getValue()).eq(SpecialTask::getSpecialId, specialId)
            .list().stream().map(SpecialTask::getId).collect(Collectors.toList());
        if (taskIdList.isEmpty()) {
            log.info("没有已发布的任务，消息消费结束，专题完成状态不进行变更");
            return;
        }
        // 判断任务是否完成
        Long count = specialTaskProgressService.getUnfinishTaskCount(taskIdList, specialId, userId);
        log.info("没有完成的任务数量为：{}", count);
        // 任务都完成状态
        // 计算任务得分情况
        BigDecimal userScoreBySpecialId = specialTaskProgressService.getUserScoreBySpecialId(userId, specialId);
        if (count == 0) {
            SpecialProgress specialProgress = specialProgressService.lambdaQuery()
                .eq(SpecialProgress::getSpecialId, specialId).eq(SpecialProgress::getUserId, userId).one();

            // 更新专题完成状态
            specialProgressService.lambdaUpdate().set(SpecialProgress::getStatus, GeneralJudgeEnum.CONFIRM.getValue())
                .set(SpecialProgress::getUpdateTime, new Date()).set(SpecialProgress::getUpdateBy, userId)
                .set(SpecialProgress::getFinishTime, new Date()).eq(SpecialProgress::getSpecialId, specialId)
                .set(SpecialProgress::getProgressPercent, 100).set(SpecialProgress::getUserScore, userScoreBySpecialId)
                .eq(SpecialProgress::getUserId, userId).update();

            // 发送资源记录同步事件
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.SPECIAL.name(),
                    specialProgress.getId(), specialId, specialProgress.getUserId(), 1, UserThreadContext.getUserId(),
                    new Date(), UserThreadContext.getUserId(), new Date())));

            // 专题完成事件
            mqProducer.sendMsg(new SpecialFinishEvent(specialId, userId, userId));
        } else {
            // 更新专题完成进度
            BigDecimal userProgressPercent = specialProgressService.getUserProgressPercent(specialId, userId);
            specialProgressService.lambdaUpdate().set(SpecialProgress::getUpdateTime, new Date())
                .set(SpecialProgress::getUpdateBy, userId).set(SpecialProgress::getFinishTime, new Date())
                .eq(SpecialProgress::getSpecialId, specialId)
                .set(SpecialProgress::getProgressPercent, userProgressPercent)
                .set(SpecialProgress::getUserScore, userScoreBySpecialId).eq(SpecialProgress::getUserId, userId)
                .update();
        }
    }

    @Override
    public UserIdpSpecialDTO getUserIdpSpecialByYear(UserIdpSpecialQuery userIdpSpecialQuery) {
        UserIdpSpecialDTO userIdpSpecial = new UserIdpSpecialDTO();
        UserDTO userInfo = userFeign.getUserById(UserThreadContext.getUserId());
        if (Optional.ofNullable(userInfo).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        userIdpSpecial.setPostName(userInfo.getPostName());
        //设置当前用户id
        userIdpSpecialQuery.setUserId(UserThreadContext.getUserId());
        //获取用户idp专题数据
        List<UserIdpSpecialVO> list = baseMapper.getSpecialIdpTreeByYear(userIdpSpecialQuery);
        List<IdpSpecialDTO> userIdpSpecialDTOList = new ArrayList<>();
        //判断专题数据是否为空
        if (!CollectionUtils.isEmpty(list)) {
            //当不为空时
            //使用迭代器获取每一个专题信息
            Iterator<UserIdpSpecialVO> iterator = list.iterator();
            //获取当前时间
            Calendar calendar = Calendar.getInstance();
            Date now = calendar.getTime();
            while (iterator.hasNext()) {
                UserIdpSpecialVO userIdpSpecialVO = iterator.next();
                //判断用户专题状态
                //如果专题未参与且已经结束，就删除专题
                if (userIdpSpecialVO.getIsOperation() == 0 && userIdpSpecialVO.getEndTime() != null && now.after(
                    userIdpSpecialVO.getEndTime())) {
                    iterator.remove();
                    continue;
                }
                IdpSpecialDTO userIdpSpecialDTO = new IdpSpecialDTO();
                //设置专题id
                userIdpSpecialDTO.setId(userIdpSpecialVO.getId());
                //设置专题名称
                userIdpSpecialDTO.setProName(userIdpSpecialVO.getSpecialName());
                //设置专题开始时间
                if (null == userIdpSpecialVO.getStartTime()) {
                    userIdpSpecialDTO.setStartTime(new Date());
                } else {
                    userIdpSpecialDTO.setStartTime(userIdpSpecialVO.getStartTime());
                }
                //设置专题是否完成
                userIdpSpecialDTO.setIsFinish(userIdpSpecialVO.getIsFinish());
                //是否可以直接进入任务: 0不可以, 1可以
                userIdpSpecialDTO.setIsOperation(userIdpSpecialVO.getIsOperation());
                //添加专题对象到userIdpSpecialDTOList
                userIdpSpecialDTOList.add(userIdpSpecialDTO);
            }
        }
        setMonthIdpList(userIdpSpecialDTOList, userIdpSpecial);
        return userIdpSpecial;
    }

    @Override
    public void export(SpecialListQuery specialListQuery) {

        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List getData(Integer pageNo, Integer pageSize) {
                ISpecialService specialService = SpringUtil.getBean("specialService", ISpecialService.class);
                specialListQuery.setExport(true);
                specialListQuery.setPageNo(pageNo);
                specialListQuery.setPageSize(pageSize);
                assert specialService != null;
                PageInfo<SpecialListDTO> specialListDTOPageInfo = specialService.queryPage(specialListQuery);
                List<Map<String, Object>> specialListExportDTOS = new ArrayList<>();
                for (SpecialListDTO specialListDTO : specialListDTOPageInfo.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(specialListDTO);
                    specialListExportDTOS.add(beanMap);
                }
                return specialListExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.SpecialFixedDate;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.SpecialFixedDate.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public List<String> getInvalidSpecialId(Collection<String> specialIdList) {
        return baseMapper.getInvalidSpecialId(specialIdList);
    }

    @Override
    public PageInfo<HomePageSpecialDTO> findSpecialHomePageList(SpecialHomePageQuery specialHomePageQuery) {
        specialHomePageQuery.setUserId(UserThreadContext.getUserId());
        if (StringUtils.isEmpty(specialHomePageQuery.getContentRule())) {
            specialHomePageQuery.setContentRule(SPECIAL.getRuleType());
        }
        PageInfo<SpecialClientDTO> sqlPageInfo =
            PageMethod.startPage(specialHomePageQuery.getPageNo(), specialHomePageQuery.getPageSize())
                .doSelectPageInfo(() -> baseMapper.selectSpecialHomePageList(specialHomePageQuery));
        PageInfo<HomePageSpecialDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(sqlPageInfo, result);

        List<SpecialClientDTO> projectPageDTOList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(projectPageDTOList)) {
            return result;
        }

        List<String> projectIds = projectPageDTOList.stream().map(SpecialClientDTO::getId).collect(Collectors.toList());
        // 查询项目图片
        Map<String, String> imageUrlsByIds = fileFeign.getImageUrlsByIds(projectIds, ImageBizType.SPECIAL_COVER.name());

        Map<String, List<CommentDetailDTO>> specialCommentMap = null;

        if (LayoutStyleEnum.SPECIAL_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())
            || LayoutStyleEnum.SPECIAL_3_8.getType().equals(specialHomePageQuery.getLayoutStyle())
            || LayoutStyleEnum.SPECIAL_UD2_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())) {
            specialCommentMap = commentFeign.findMostLikeCommentByResourceId(projectIds);
        }
        List<HomePageSpecialDTO> list = new ArrayList<>();
        HomePageSpecialDTO headPageSpecialVo = null;
        HomePageSpecialDTO.SpecialTask specialTask;
        for (SpecialClientDTO projectVo : projectPageDTOList) {
            headPageSpecialVo = new HomePageSpecialDTO();
            headPageSpecialVo.setId(projectVo.getId());
            headPageSpecialVo.setTitle(projectVo.getTitle());
            headPageSpecialVo.setImageUrl(imageUrlsByIds.get(projectVo.getId()));
            //设置时间 或者 周期
            headPageSpecialVo.setType(projectVo.getType());
            if (headPageSpecialVo.getType() == FIXED_TIME) {
                headPageSpecialVo.setStartTime(projectVo.getStartTime());
                headPageSpecialVo.setEndTime(projectVo.getEndTime());
            } else {
                headPageSpecialVo.setPeriod(projectVo.getCycleDay());
            }

            headPageSpecialVo.setProgress(
                NumberOperationUtils.getPercent(projectVo.getFinishCount(), projectVo.getTaskCount(), 1).toString());

            // 数据组装
            extracted(specialHomePageQuery, projectVo, specialCommentMap, headPageSpecialVo);

            if (LayoutStyleEnum.SPECIAL_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())
                || LayoutStyleEnum.SPECIAL_UD2_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())) {
                LambdaQueryWrapper<SpecialTask> projectTaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
                projectTaskLambdaQueryWrapper.eq(SpecialTask::getSpecialId, projectVo.getId());
                projectTaskLambdaQueryWrapper.eq(SpecialTask::getIsDel, DelEnum.NOT_DELETE.getValue());
                projectTaskLambdaQueryWrapper.eq(SpecialTask::getIsPublish, PublishEnum.PUBLISHED.getValue());
                projectTaskLambdaQueryWrapper.orderByAsc(SpecialTask::getSort);
                List<SpecialTask> projectTasks = specialTaskService.list(projectTaskLambdaQueryWrapper);
                List<HomePageSpecialDTO.SpecialTask> specialTasks = new ArrayList<>();
                for (SpecialTask projectTask : projectTasks) {
                    specialTask = new HomePageSpecialDTO().createSpecialTask();
                    specialTask.setId(projectTask.getId());
                    specialTask.setTaskName(projectTask.getTaskName());
                    specialTask.setTaskType(projectTask.getTaskType());
                    specialTasks.add(specialTask);
                }
                headPageSpecialVo.setSpecialTasks(specialTasks);
            }
            list.add(headPageSpecialVo);
        }

        result.setList(list);
        return result;
    }

    // 数据组装
    private static void extracted(SpecialHomePageQuery specialHomePageQuery, SpecialClientDTO projectVo,
        Map<String, List<CommentDetailDTO>> specialCommentMap, HomePageSpecialDTO headPageSpecialVo) {
        HomePageSpecialDTO.SpecialComment specialComment;
        if (LayoutStyleEnum.SPECIAL_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())
            || LayoutStyleEnum.SPECIAL_3_8.getType().equals(specialHomePageQuery.getLayoutStyle())
            || LayoutStyleEnum.SPECIAL_UD2_1_1.getType().equals(specialHomePageQuery.getLayoutStyle())) {

            List<CommentDetailDTO> mostLikeComment = specialCommentMap.get(projectVo.getId());

            List<HomePageSpecialDTO.SpecialComment> specialComments = new ArrayList<>();
            for (CommentDetailDTO postcomment : mostLikeComment) {
                specialComment = new HomePageSpecialDTO().createSpecialComment();
                specialComment.setReplyUserName(postcomment.getFullName());
                specialComment.setReplyUserNameHeadImageUrl(postcomment.getHeadImage());
                specialComment.setReplyContent(postcomment.getContent());
                specialComments.add(specialComment);
            }
            headPageSpecialVo.setSpecialComments(specialComments);
        }
    }

    @Override
    public List<ResourceBaseDTO> getSpecialBaseList(ResourceBaseQuery resourceBaseQuery) {
        return baseMapper.getSpecialBaseList(resourceBaseQuery);
    }

    @Override
    public List<ViewLimitTypeDTO> getViewLimit(String id) {
        //下发范围
        List<SpecialViewLimit> viewLimitByResourceId = specialViewLimitComponent.getViewLimitByResourceId(id);
        return specialViewLimitComponent.getViewLimitTypeDTO(viewLimitByResourceId);
    }

    @Override
    public SpecialTaskDetailNumDTO getUserSpecialTaskDetail(String id, String userId) {
        return baseMapper.getUserSpecialTaskDetail(id, userId);
    }

    @Override
    public ResourceDeleteInfoDTO getSpecialIsDelById(String resourceId) {
        return baseMapper.getSpecialIsDelById(resourceId);
    }

    @Override
    public Long getSpecialStudentNumById(String specialId) {
        return specialProgressService.getSpecialStudentNumById(specialId);
    }

}
