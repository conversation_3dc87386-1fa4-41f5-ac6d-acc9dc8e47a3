<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.wunding</groupId>
    <artifactId>learn</artifactId>
    <version>${revision}</version>
    <name>learn</name>
    <packaging>pom</packaging>
    <description>learn</description>

    <properties>
        <java.version>21</java.version>
        <repackage.classifier/>
        <native-buildtools.version>0.9.10</native-buildtools.version>
        <spring-native.version>0.11.3</spring-native.version>
        <prometheus.version>1.11.0</prometheus.version>

        <!-- OpenTelemetry 版本管理 -->
        <opentelemetry.version>1.24.0</opentelemetry.version>
        <opentelemetry.alpha.version>1.24.0-alpha</opentelemetry.alpha.version>
        <micrometer-tracing.version>1.0.4</micrometer-tracing.version>

        <lombok.version>1.18.30</lombok.version>
        <mybatis-plus.version>3.5.8</mybatis-plus.version>
        <spring-boot.version>3.3.6</spring-boot.version>
        <spring-cloud.version>2023.0.4</spring-cloud.version>
        <elasticsearch.version>8.4.3</elasticsearch.version>
        <jakarta.json-api.version>2.0.1</jakarta.json-api.version>
        <jsoup.version>1.14.3</jsoup.version>
        <dingtalk-sdk.version>2.0.14</dingtalk-sdk.version>
        <okhttp.version>4.9.3</okhttp.version>
        <netty.version>4.1.115.Final</netty.version>

        <image.registry>registry-test.wunding.com</image.registry>
        <image.registry.path>/wunding/</image.registry.path>
        <image.tag>v7.10.0-sit</image.tag>
        <skipDockerImage>true</skipDockerImage>
        <skipSingleDockerImage>true</skipSingleDockerImage>
        <docker.file>Dockerfile21.maven</docker.file>
        <docker.platforms>linux/amd64</docker.platforms>
        <learn.version>v7.10.10-SNAPSHOT</learn.version>
        <revision>${learn.version}</revision>

        <common.version>${learn.version}</common.version>
        <common.base.version>${learn.version}</common.base.version>
        <common.mq.version>${learn.version}</common.mq.version>
        <common.aop.version>${learn.version}</common.aop.version>
        <common.jwt.version>${learn.version}</common.jwt.version>
        <common.interceptor.version>${learn.version}</common.interceptor.version>
        <common.context.version>${learn.version}</common.context.version>
        <common.datasource.version>${learn.version}</common.datasource.version>
        <common.elastic.version>${learn.version}</common.elastic.version>
        <common.bean.version>${learn.version}</common.bean.version>
        <common.i18n.version>${learn.version}</common.i18n.version>
        <common.exception.version>${learn.version}</common.exception.version>
        <common.feign.version>${learn.version}</common.feign.version>
        <common.constant.version>${learn.version}</common.constant.version>
        <common.util.version>${learn.version}</common.util.version>
        <common.mybatis.version>${learn.version}</common.mybatis.version>
        <common.redis.version>${learn.version}</common.redis.version>
        <common.swagger.version>${learn.version}</common.swagger.version>
        <common.thread.version>${learn.version}</common.thread.version>
        <common.xxljob.version>${learn.version}</common.xxljob.version>
        <common.category.version>${learn.version}</common.category.version>
        <common.ai.outline.version>${learn.version}</common.ai.outline.version>
        <common.consumer.version>${learn.version}</common.consumer.version>
        <common.field.sync.version>${learn.version}</common.field.sync.version>
        <common.library.record.version>${learn.version}</common.library.record.version>
        <common.viewlimit.version>${learn.version}</common.viewlimit.version>
        <common.multi.language.version>${learn.version}</common.multi.language.version>
        <common.directory.version>${learn.version}</common.directory.version>
        <common.form.version>${learn.version}</common.form.version>
        <common.table.partition.version>${learn.version}</common.table.partition.version>
        <common.poster.share.version>${learn.version}</common.poster.share.version>
        <maxkb.api.version>${learn.version}</maxkb.api.version>
        <maxkb.service.version>${learn.version}</maxkb.service.version>

        <sync.common.version>${learn.version}</sync.common.version>
        <sync.service.version>${learn.version}</sync.service.version>

        <file.version>${learn.version}</file.version>
        <file.api.version>${learn.version}</file.api.version>
        <file.service.version>${learn.version}</file.service.version>
        <trans.api.version>${learn.version}</trans.api.version>
        <trans.common.version>${learn.version}</trans.common.version>
        <trans.service.version>${learn.version}</trans.service.version>

        <user.version>${learn.version}</user.version>
        <user.api.version>${learn.version}</user.api.version>
        <user.service.version>${learn.version}</user.service.version>
        <flowable.api.version>${learn.version}</flowable.api.version>
        <flowable.service.version>${learn.version}</flowable.service.version>

        <appraise.version>${learn.version}</appraise.version>
        <appraise.api.version>${learn.version}</appraise.api.version>
        <appraise.service.version>${learn.version}</appraise.service.version>

        <business.view.version>${learn.version}</business.view.version>
        <business.view.api.version>${learn.version}</business.view.api.version>
        <business.view.service.version>${learn.version}</business.view.service.version>

        <certification.version>${learn.version}</certification.version>
        <certification.api.version>${learn.version}</certification.api.version>
        <certification.service.version>${learn.version}</certification.service.version>

        <comment.version>${learn.version}</comment.version>
        <comment.api.version>${learn.version}</comment.api.version>
        <comment.service.version>${learn.version}</comment.service.version>

        <course.version>${learn.version}</course.version>
        <course.api.version>${learn.version}</course.api.version>
        <course.service.version>${learn.version}</course.service.version>

        <evaluation.version>${learn.version}</evaluation.version>
        <evaluation.api.version>${learn.version}</evaluation.api.version>
        <evaluation.service.version>${learn.version}</evaluation.service.version>

        <exam.version>${learn.version}</exam.version>
        <exam.api.version>${learn.version}</exam.api.version>
        <exam.service.version>${learn.version}</exam.service.version>

        <example.version>${learn.version}</example.version>
        <example.api.version>${learn.version}</example.api.version>
        <example.service.version>${learn.version}</example.service.version>

        <excitation.version>${learn.version}</excitation.version>
        <excitation.api.version>${learn.version}</excitation.api.version>
        <excitation.service.version>${learn.version}</excitation.service.version>

        <forum.version>${learn.version}</forum.version>
        <forum.api.version>${learn.version}</forum.api.version>
        <forum.service.version>${learn.version}</forum.service.version>

        <info.version>${learn.version}</info.version>
        <info.api.version>${learn.version}</info.api.version>
        <info.service.version>${learn.version}</info.service.version>

        <lecturer.version>${learn.version}</lecturer.version>
        <lecturer.api.version>${learn.version}</lecturer.api.version>
        <lecturer.service.version>${learn.version}</lecturer.service.version>

        <live.version>${learn.version}</live.version>
        <live.api.version>${learn.version}</live.api.version>
        <live.service.version>${learn.version}</live.service.version>

        <market.version>${learn.version}</market.version>
        <market.api.version>${learn.version}</market.api.version>
        <market.service.version>${learn.version}</market.service.version>

        <project.version>${learn.version}</project.version>
        <project.api.version>${learn.version}</project.api.version>
        <project.service.version>${learn.version}</project.service.version>

        <promoted.game.version>${learn.version}</promoted.game.version>
        <promoted.game.api.version>${learn.version}</promoted.game.api.version>
        <promoted.game.service.version>${learn.version}</promoted.game.service.version>

        <push.version>${learn.version}</push.version>
        <push.api.version>${learn.version}</push.api.version>
        <push.service.version>${learn.version}</push.service.version>

        <reading.version>${learn.version}</reading.version>
        <reading.api.version>${learn.version}</reading.api.version>
        <reading.service.version>${learn.version}</reading.service.version>

        <recruiting.version>${learn.version}</recruiting.version>
        <recruiting.api.version>${learn.version}</recruiting.api.version>
        <recruting.service.version>${learn.version}</recruting.service.version>

        <special.version>${learn.version}</special.version>
        <special.api.version>${learn.version}</special.api.version>
        <special.service.version>${learn.version}</special.service.version>

        <survey.version>${learn.version}</survey.version>
        <survey.api.version>${learn.version}</survey.api.version>
        <survey.service.version>${learn.version}</survey.service.version>

        <apply.version>${learn.version}</apply.version>
        <apply.api.version>${learn.version}</apply.api.version>
        <apply.service.version>${learn.version}</apply.service.version>

        <operation.version>${learn.version}</operation.version>
        <operation.api.version>${learn.version}</operation.api.version>
        <operation.service.version>${learn.version}</operation.service.version>

        <march.version>${learn.version}</march.version>
        <march.api.version>${learn.version}</march.api.version>
        <march.service.version>${learn.version}</march.service.version>

        <plan.version>${learn.version}</plan.version>
        <plan.api.version>${learn.version}</plan.api.version>
        <plan.service.version>${learn.version}</plan.service.version>

        <train.version>${learn.version}</train.version>
        <train.api.version>${learn.version}</train.api.version>
        <train.service.version>${learn.version}</train.service.version>

        <websocket.version>${learn.version}</websocket.version>
        <websocket.api.version>${learn.version}</websocket.api.version>
        <websocket.service.version>${learn.version}</websocket.service.version>

        <tenant.version>${learn.version}</tenant.version>
        <tenant.api.version>${learn.version}</tenant.api.version>
        <tenant.service.version>${learn.version}</tenant.service.version>

        <march.version>${learn.version}</march.version>
        <march.api.version>${learn.version}</march.api.version>
        <march.service.version>${learn.version}</march.service.version>

        <payment.version>${learn.version}</payment.version>
        <payment.api.version>${learn.version}</payment.api.version>
        <payment.service.version>${learn.version}</payment.service.version>

    </properties>

    <modules>
        <module>common</module>
        <module>comment</module>
        <module>file</module>
        <module>user</module>
        <module>exam</module>
        <module>course</module>
        <module>lecturer</module>
        <module>survey</module>
        <module>forum</module>
        <module>live</module>
        <module>info</module>
        <module>project</module>
        <module>appraise</module>
        <module>special</module>
        <module>recruiting</module>
        <module>excitation</module>
        <module>promoted-game</module>
        <module>reading</module>
        <module>example</module>
        <module>business-view</module>
        <module>certification</module>
        <module>push</module>
        <module>market</module>
        <module>evaluation</module>
        <module>apply</module>
        <module>operation</module>
        <module>single</module>
        <module>tenant</module>
        <module>plan</module>
        <module>train</module>
        <module>websocket</module>
        <module>trans</module>
        <module>march</module>
        <module>payment</module>
        <module>sync</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- CAS Client -->
            <dependency>
                <groupId>org.jasig.cas.client</groupId>
                <artifactId>cas-client-core</artifactId>
                <version>3.6.4</version>
            </dependency>

            <!-- Spring Security CAS -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-cas</artifactId>
                <version>6.3.0</version>
            </dependency>


            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>1.2.23</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.0.33</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>6.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>2.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.13.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.0-jre</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>4.1.2</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.mwiede/jsch -->
            <dependency>
                <groupId>com.github.mwiede</groupId>
                <artifactId>jsch</artifactId>
                <version>2.27.2</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.11.1</version>
            </dependency>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>2.31.64</version>
            </dependency>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sts</artifactId>
                <version>2.17.138</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>3.3.6</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.39.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-serializer-kryo</artifactId>
                <version>1.4.2</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.1</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-base</artifactId>
                <version>${common.base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-context</artifactId>
                <version>${common.context.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-datasource</artifactId>
                <version>${common.datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-elastic</artifactId>
                <version>${common.elastic.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-mq</artifactId>
                <version>${common.mq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-aop</artifactId>
                <version>${common.aop.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-jwt</artifactId>
                <version>${common.jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-flowable-api</artifactId>
                <version>${flowable.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-flowable-service</artifactId>
                <version>${flowable.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-interceptor</artifactId>
                <version>${common.interceptor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-exception</artifactId>
                <version>${common.exception.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-feign</artifactId>
                <version>${common.feign.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-i18n</artifactId>
                <version>${common.i18n.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-bean</artifactId>
                <version>${common.bean.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-constant</artifactId>
                <version>${common.constant.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-util</artifactId>
                <version>${common.util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-mybatis</artifactId>
                <version>${common.mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-redis</artifactId>
                <version>${common.redis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-swagger</artifactId>
                <version>${common.swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-thread</artifactId>
                <version>${common.thread.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-xxljob</artifactId>
                <version>${common.xxljob.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-category</artifactId>
                <version>${common.category.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-ai-outline</artifactId>
                <version>${common.ai.outline.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-directory</artifactId>
                <version>${common.directory.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-consumer</artifactId>
                <version>${common.consumer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-field-sync</artifactId>
                <version>${common.field.sync.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-library-record</artifactId>
                <version>${common.library.record.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-viewlimit</artifactId>
                <version>${common.viewlimit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-multi-language</artifactId>
                <version>${common.multi.language.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-form</artifactId>
                <version>${common.form.version}</version>
            </dependency>

            <dependency>
                <artifactId>learn-common-table-partition</artifactId>
                <groupId>com.wunding</groupId>
                <version>${common.table.partition.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-poster-share</artifactId>
                <version>${common.poster.share.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-maxkb-api</artifactId>
                <version>${maxkb.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-sync-common</artifactId>
                <version>${sync.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-sync-service</artifactId>
                <version>${sync.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-trans-common</artifactId>
                <version>${trans.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-trans-api</artifactId>
                <version>${trans.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-file-api</artifactId>
                <version>${file.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-user-api</artifactId>
                <version>${user.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-websocket-api</artifactId>
                <version>${websocket.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-websocket-api-impl</artifactId>
                <version>${websocket.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-websocket-service</artifactId>
                <version>${websocket.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-appraise-api</artifactId>
                <version>${appraise.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-business-view-api</artifactId>
                <version>${business.view.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-certification-api</artifactId>
                <version>${certification.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-comment-api</artifactId>
                <version>${comment.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-course-api</artifactId>
                <version>${course.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-evaluation-api</artifactId>
                <version>${evaluation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-exam-api</artifactId>
                <version>${exam.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-example-api</artifactId>
                <version>${example.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-excitation-api</artifactId>
                <version>${excitation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-forum-api</artifactId>
                <version>${forum.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-info-api</artifactId>
                <version>${info.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-lecturer-api</artifactId>
                <version>${lecturer.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-live-api</artifactId>
                <version>${live.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-market-api</artifactId>
                <version>${market.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-project-api</artifactId>
                <version>${project.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-promotedgame-api</artifactId>
                <version>${promoted.game.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-push-api</artifactId>
                <version>${push.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-reading-api</artifactId>
                <version>${reading.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-recruiting-api</artifactId>
                <version>${recruiting.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-special-api</artifactId>
                <version>${special.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-survey-api</artifactId>
                <version>${survey.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-file-service</artifactId>
                <version>${file.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-user-api-impl</artifactId>
                <version>${user.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-user-login</artifactId>
                <version>${user.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-user-service</artifactId>
                <version>${user.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-appraise-service</artifactId>
                <version>${appraise.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-business-view-service</artifactId>
                <version>${business.view.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-certification-service</artifactId>
                <version>${certification.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-comment-service</artifactId>
                <version>${comment.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-course-service</artifactId>
                <version>${course.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-evaluation-service</artifactId>
                <version>${evaluation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-exam-service</artifactId>
                <version>${exam.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-example-service</artifactId>
                <version>${example.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-excitation-service</artifactId>
                <version>${excitation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-forum-service</artifactId>
                <version>${forum.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-info-service</artifactId>
                <version>${info.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-lecturer-service</artifactId>
                <version>${lecturer.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-live-service</artifactId>
                <version>${live.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-market-service</artifactId>
                <version>${market.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-project-service</artifactId>
                <version>${project.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-promotedgame-service</artifactId>
                <version>${promoted.game.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-push-service</artifactId>
                <version>${push.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-reading-service</artifactId>
                <version>${reading.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-recruiting-service</artifactId>
                <version>${recruiting.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-special-service</artifactId>
                <version>${special.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-survey-service</artifactId>
                <version>${survey.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-apply-api</artifactId>
                <version>${apply.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-plan-api</artifactId>
                <version>${plan.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-operation-api</artifactId>
                <version>${operation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-march-api</artifactId>
                <version>${march.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-march-service</artifactId>
                <version>${march.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-train-api</artifactId>
                <version>${train.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-apply-service</artifactId>
                <version>${apply.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-plan-service</artifactId>
                <version>${plan.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-operation-service</artifactId>
                <version>${operation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-train-service</artifactId>
                <version>${train.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-flowable-api</artifactId>
                <version>${flowable.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-flowable-service</artifactId>
                <version>${flowable.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-common-ai-outline</artifactId>
                <version>${common.ai.outline.version}</version>
            </dependency>


            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>2.6.0</version>
            </dependency>

            <dependency>
                <groupId>io.opentracing.contrib</groupId>
                <artifactId>opentracing-spring-jaeger-cloud-starter</artifactId>
                <version>3.3.1</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-tenant-api</artifactId>
                <version>${tenant.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-march-api</artifactId>
                <version>${march.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-march-service</artifactId>
                <version>${march.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-maxkb-service</artifactId>
                <version>${maxkb.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-payment-api</artifactId>
                <version>${payment.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wunding</groupId>
                <artifactId>learn-payment-service</artifactId>
                <version>${payment.api.version}</version>
            </dependency>
            <dependency>
                <artifactId>wechatpay-java</artifactId>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <version>0.2.10</version>
            </dependency>

            <!-- Jaeger Exporter -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-exporter-jaeger</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <!-- OpenTelemetry SDK -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-sdk</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <!-- OpenTelemetry API -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-api</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>

            <!-- 添加OpenTelemetry Context传播依赖 -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-extension-trace-propagators</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-instrumentation-api</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-spring-boot-starter</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-spring-webmvc</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-feign-core-3.0</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
<!--        <repository>
            <id>pentaho-public</id>
            <name>Pentaho Public</name>
            <url>https://repo.orl.eng.hitachivantara.com/artifactory/pnt-mvn/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>-->
        <!--<repository>
            <id>com.e-iceblue</id>
            <url>http://repo.e-iceblue.cn/repository/maven-public/</url>
        </repository>-->
        <repository>
            <id>spring-releases</id>
            <name>Spring Releases</name>
            <url>https://repo.spring.io/release</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus</id>
            <url>http://192.168.0.100:8081/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <distributionManagement>

        <repository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>releases</id>
            <!--自定义名称-->
            <name>releases</name>
            <!--仓库地址-->
            <url>http://192.168.0.100:8081/nexus/content/repositories/releases/</url>
        </repository>
        <!--快照版本仓库-->
        <snapshotRepository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>snapshots</id>
            <!--自定义名称-->
            <name>snapshots</name>
            <!--仓库地址-->
            <url>http://192.168.0.100:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>spring-releases</id>
            <name>Spring Releases</name>
            <url>https://repo.spring.io/release</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <profiles>
        <profile>
            <id>saas</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <single.skip>true</single.skip>
                <spring-boot.repackage.skip>false</spring-boot.repackage.skip>
            </properties>
        </profile>
        <profile>
            <id>single</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <single.skip>false</single.skip>
                <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.4.1</version>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <flattenedPomFilename>pom-xml-flattened</flattenedPomFilename>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
