package com.wunding.learn.project.api.dto;

import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <p>
 * 学习项目详情DTO
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/14 15:58
 */
@Data
@Schema(name = "ProjectDetailDTO", description = "学习项目详情对象")
public class ProjectDetailDTO {

    @Schema(description = "阶段ID")
    private String stageId;


    @Schema(description = "阶段名称")
    private String stageName;

    @Schema(description = "引用资源的ID,如培训项目ID")
    private String referencedId;

    @Schema(description = "引用资源的名称，比如培训项目的名称")
    private String referencedName;


    @Schema(description = "项目id")
    private String id;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "关联计划Id")
    private String trainPlanId;

    @Schema(description = "关联计划名称")
    private String trainPlanName;

    @Schema(description = "项目描述 说明")
    private String proDesc;

    @Schema(description = "封面地址")
    private String coverImagePath;

    @Schema(description = "封面全路径地址")
    private String coverImageUrl;

    @Schema(description = "项目类型")
    private String proMethod;

    @Schema(description = "计划类型 0固定日期，1固定周期")
    private Integer type;

    @Schema(description = "固定日期开始时间")
    private Date startTime;

    @Schema(description = "固定日期结束时间")
    private Date endTime;


    @Schema(description = "班主任名称")
    private String leaderName;

    @Schema(description = "班主任用户id")
    private String leaderId;

    @Schema(description = "班主任不参与统计 0-否，班主任参与统计 1是，班主任不参与统计")
    private Integer leaderDontStat;


    @Schema(description = "活动开始时间")
    private Date activityStartTime;

    @Schema(description = "活动结束时间")
    private Date activityEndTime;

    @Schema(description = "是否允许项目内激励兑换(0否 1是)")
    private Integer enableExcitationExchange;

    @Schema(description = "下发基本信息")
    private ViewLimitBaseInfoDTO limit;

    @Schema(description = "学习项目阶段")
    private List<ProphaseSaveDTO> prophaseSaveDTOS;

    @Schema(description = "学习项目栏目 开通应用")
    private List<String> projectItem;


    @Data
    @Schema(name = "ProphaseSaveDTO", description = "学习项目阶段对象")
    public static class ProphaseSaveDTO {

        @Schema(description = "项目阶段id")
        private String id;

        @Schema(description = "项目阶段名称")
        private String name;

        @Schema(description = "项目阶段排序")
        private Long sort;
    }


    @Schema(description = "是否发布 0不 1是")
    private Integer isPublish;
}
