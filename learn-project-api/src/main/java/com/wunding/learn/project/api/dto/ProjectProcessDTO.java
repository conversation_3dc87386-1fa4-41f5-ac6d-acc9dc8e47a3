package com.wunding.learn.project.api.dto;

import com.wunding.learn.flowable.api.dto.BaseAuditStatusDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 学习项目详情审核DTO
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@Schema(name = "ProjectProcessDTO", description = "学习项目详情审核对象")
public class ProjectProcessDTO extends BaseAuditStatusDTO {

    @Schema(description = "关联计划Id")
    private String trainPlanId;

    @Schema(description = "关联计划名称")
    private String trainPlanName;

    @Schema(description = "项目id")
    private String id;

    @Schema(description = "项目名称")
    private String proName;


    @Schema(description = "项目类型")
    private String proMethod;

    @Schema(description = "项目描述 说明")
    private String proDesc;


    @Schema(description = "计划类型 0固定日期，1固定周期")
    private Integer type;

    @Schema(description = "固定日期开始时间")
    private Date startTime;

    @Schema(description = "固定日期结束时间")
    private Date endTime;

    @Schema(description = "固定周期天数")
    private Long cycleDay;


    @Schema(description = "班主任名称")
    private String leaderName;

    @Schema(description = "班主任用户id")
    private String leaderId;


    @Schema(description = "学习项目阶段")
    private List<ProjectProcessDTO.ProphaseSaveDTO> prophaseSaveDTOS;


    @Data
    @Schema(name = "ProphaseSaveDTO", description = "学习项目阶段对象")
    public static class ProphaseSaveDTO {

        @Schema(description = "项目阶段id")
        private String id;

        @Schema(description = "项目阶段名称")
        private String name;

        @Schema(description = "项目阶段排序")
        private Long sort;
    }


    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见", required = true)
    private Integer viewType;
    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;
    @Schema(description = "创建者id")
    private String createBy;

    @Schema(description = "教室名称")
    private String roomName;

    @Schema(description = "备注")
    private String mark;

    @Schema(description = "地点")
    private String address;

    @Schema(description = "下发基本信息")
    private ViewLimitBaseInfoDTO limit;


}
