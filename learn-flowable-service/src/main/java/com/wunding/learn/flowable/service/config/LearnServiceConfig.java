package com.wunding.learn.flowable.service.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p> 用于从 application.yml 中读取服务的配置
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-07-02
 */
@Data
@Component("learnServices")
@ConfigurationProperties(prefix = "learn.service")
public class LearnServiceConfig {

    private List<ServiceInfo> resourceFeign;

    /**
     * <p> 内部类，表示一个服务的信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
     * @since 2025-07-02
     */
    @Data
    public static class ServiceInfo {

        private String name;

        private String url;
    }
}
