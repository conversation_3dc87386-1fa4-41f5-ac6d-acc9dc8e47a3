package com.wunding.learn.flowable.service.config;

import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.flowable.api.feign.ResourceFeign;
import com.wunding.learn.flowable.service.config.LearnServiceConfig.ServiceInfo;
import feign.Client;
import feign.Contract;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.codec.ErrorDecoder;
import feign.slf4j.Slf4jLogger;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;

/**
 * 使用 ImportBeanDefinitionRegistrar 动态注册多个 ResourceFeign
 */
public class AutoRegisterResourceFeignRegistrar implements ImportBeanDefinitionRegistrar, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        // 从 registry 获取 ApplicationContext
        BeanDefinition learnServices = registry.getBeanDefinition("learnServices");

        // 定义 logger
        Logger slf4jLogger = new Slf4jLogger(ResourceFeign.class);
        List<ServiceInfo> resourceFeign = (List<ServiceInfo>) learnServices.getAttribute("resourceFeign");
        for (ServiceInfo serviceInfo : resourceFeign) {
            String url = buildUrl(serviceInfo.getUrl(), serviceInfo.getName());
            registerFeignBean(registry, serviceInfo.getName() + "ResourceFeign", url, slf4jLogger);
        }
    }

    private void registerFeignBean(BeanDefinitionRegistry registry, String beanName, String url, Logger logger) {
        GenericBeanDefinition beanDefinition = new GenericBeanDefinition();
        beanDefinition.setBeanClass(ResourceFeign.class);
        // 如果有 /, 去掉多余 /
        if (url.endsWith(StringPool.SLASH)) {
            url = StringUtils.removeEnd(url, StringPool.SLASH);
        }
        String finalUrl = url;
        beanDefinition.setInstanceSupplier(() -> {
            Decoder decoder = applicationContext.getBean(Decoder.class);
            Encoder encoder = applicationContext.getBean(Encoder.class);
            ErrorDecoder errorDecoder = applicationContext.getBean(ErrorDecoder.class);
            Client feignClient = applicationContext.getBean(Client.class);
            Contract contract = applicationContext.getBean(Contract.class);
            RequestInterceptor requestInterceptor = applicationContext.getBean(RequestInterceptor.class);

            return Feign.builder()
                .client(feignClient)
                .errorDecoder(errorDecoder)
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .logger(logger)
                .requestInterceptor(requestInterceptor)
                .target(ResourceFeign.class, finalUrl);
        });

        // Lazy加载
        beanDefinition.setLazyInit(true);

        registry.registerBeanDefinition(beanName, beanDefinition);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    private String buildUrl(String baseUrl, String servicePath) {
        if (baseUrl.endsWith(StringPool.SLASH)) {
            baseUrl = StringUtils.removeEnd(baseUrl, StringPool.SLASH);
        }
        return baseUrl + "/" + servicePath;
    }

}
