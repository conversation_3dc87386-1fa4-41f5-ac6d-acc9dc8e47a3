package com.wunding.learn.flowable.service.config;

import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.flowable.api.feign.ResourceFeign;
import com.wunding.learn.flowable.service.config.LearnServiceConfig.ServiceInfo;
import feign.Client;
import feign.Contract;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.codec.ErrorDecoder;
import feign.slf4j.Slf4jLogger;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;

/**
 * 使用 ImportBeanDefinitionRegistrar 动态注册多个 ResourceFeign
 */
@Slf4j
public class AutoRegisterResourceFeignRegistrar implements ImportBeanDefinitionRegistrar, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        // 定义 logger
        Logger slf4jLogger = new Slf4jLogger(ResourceFeign.class);

        try {
            LearnServiceConfig learnServiceConfig = getLearnServiceConfig(registry);

            if (learnServiceConfig != null && learnServiceConfig.getResourceFeign() != null) {
                List<ServiceInfo> resourceFeignList = learnServiceConfig.getResourceFeign();
                log.info("Found {} ResourceFeign services to register", resourceFeignList.size());

                for (ServiceInfo serviceInfo : resourceFeignList) {
                    String url = buildUrl(serviceInfo.getUrl(), serviceInfo.getName());
                    registerFeignBean(registry, serviceInfo.getName() + "ResourceFeign", url, slf4jLogger);
                    log.info("Registered ResourceFeign: {} -> {}", serviceInfo.getName(), url);
                }
            } else {
                log.warn("No ResourceFeign services found to register");
            }
        } catch (Exception e) {
            log.error("Failed to register ResourceFeign beans: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取 LearnServiceConfig 配置的多种方式
     */
    private LearnServiceConfig getLearnServiceConfig(BeanDefinitionRegistry registry) {
        // 方案一：通过 ApplicationContext 获取已注册的 Bean
        if (applicationContext != null) {
            try {
                return applicationContext.getBean("learnServices", LearnServiceConfig.class);
            } catch (Exception e) {
                log.debug("Failed to get learnServices bean from ApplicationContext: {}", e.getMessage());
            }
        }

        // 方案二：通过 Environment 和 Binder 从配置文件获取
        if (applicationContext != null) {
            try {
                Environment environment = applicationContext.getEnvironment();
                return Binder.get(environment)
                    .bind("learn.services", LearnServiceConfig.class)
                    .orElse(null);
            } catch (Exception e) {
                log.debug("Failed to bind configuration from Environment: {}", e.getMessage());
            }
        }

        // 方案三：从 BeanDefinition 获取
        if (registry.containsBeanDefinition("learnServices")) {
            try {
                BeanDefinition beanDefinition = registry.getBeanDefinition("learnServices");
                return getConfigFromBeanDefinition(beanDefinition);
            } catch (Exception e) {
                log.debug("Failed to get configuration from BeanDefinition: {}", e.getMessage());
            }
        }

        return null;
    }

    /**
     * 从 BeanDefinition 中提取配置对象
     */
    private LearnServiceConfig getConfigFromBeanDefinition(BeanDefinition beanDefinition) {
        try {
            // 1. 如果是 GenericBeanDefinition，尝试获取 bean class 并实例化
            if (beanDefinition instanceof GenericBeanDefinition genericBeanDef) {
                Class<?> beanClass = genericBeanDef.getBeanClass();
                if (LearnServiceConfig.class.isAssignableFrom(beanClass)) {
                    return (LearnServiceConfig) beanClass.getDeclaredConstructor().newInstance();
                }
            }

            // 2. 从属性值中获取
            if (beanDefinition.getPropertyValues().contains("resourceFeign")) {
                Object propertyValue = beanDefinition.getPropertyValues().getPropertyValue("resourceFeign").getValue();
                if (propertyValue instanceof List<?> list) {
                    LearnServiceConfig config = new LearnServiceConfig();
                    // 这里需要根据实际的 LearnServiceConfig 结构来设置
                    config.setResourceFeign((List<ServiceInfo>) list);
                    return config;
                }
            }

            // 3. 从 bean 的 attributes 中获取
            Object attribute = beanDefinition.getAttribute("learnServiceConfig");
            if (attribute instanceof LearnServiceConfig) {
                return (LearnServiceConfig) attribute;
            }

        } catch (Exception e) {
            log.debug("Failed to extract config from BeanDefinition: {}", e.getMessage());
        }

        return null;
    }

    private void registerFeignBean(BeanDefinitionRegistry registry, String beanName, String url, Logger logger) {
        GenericBeanDefinition beanDefinition = new GenericBeanDefinition();
        beanDefinition.setBeanClass(ResourceFeign.class);
        // 如果有 /, 去掉多余 /
        if (url.endsWith(StringPool.SLASH)) {
            url = StringUtils.removeEnd(url, StringPool.SLASH);
        }
        String finalUrl = url;
        beanDefinition.setInstanceSupplier(() -> {
            Decoder decoder = applicationContext.getBean(Decoder.class);
            Encoder encoder = applicationContext.getBean(Encoder.class);
            ErrorDecoder errorDecoder = applicationContext.getBean(ErrorDecoder.class);
            Client feignClient = applicationContext.getBean(Client.class);
            Contract contract = applicationContext.getBean(Contract.class);
            RequestInterceptor requestInterceptor = applicationContext.getBean(RequestInterceptor.class);

            return Feign.builder()
                .client(feignClient)
                .errorDecoder(errorDecoder)
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .logger(logger)
                .requestInterceptor(requestInterceptor)
                .target(ResourceFeign.class, finalUrl);
        });

        // Lazy加载
        beanDefinition.setLazyInit(true);

        registry.registerBeanDefinition(beanName, beanDefinition);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    private String buildUrl(String baseUrl, String servicePath) {
        if (baseUrl.endsWith(StringPool.SLASH)) {
            baseUrl = StringUtils.removeEnd(baseUrl, StringPool.SLASH);
        }
        return baseUrl + "/" + servicePath;
    }

}
