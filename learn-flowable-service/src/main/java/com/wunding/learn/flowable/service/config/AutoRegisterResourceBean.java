package com.wunding.learn.flowable.service.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({FeignClientsConfiguration.class, AutoRegisterResourceFeignRegistrar.class})
@ConditionalOnExpression("!'${spring.application.name}'.equals('mlearn-single')")
public class AutoRegisterResourceBean {
}
